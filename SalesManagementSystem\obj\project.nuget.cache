{"version": 2, "dgSpecHash": "FL23XHf3huY=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.reportingservices.reportviewercontrol.winforms\\150.1652.0\\microsoft.reportingservices.reportviewercontrol.winforms.150.1652.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.types\\14.0.314.76\\microsoft.sqlserver.types.14.0.314.76.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.119\\stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.119\\system.data.sqlite.core.1.0.119.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "Microsoft.ReportingServices.ReportViewerControl.Winforms 150.1652.0 depends on Microsoft.SqlServer.Types (>= 14.0.0) but Microsoft.SqlServer.Types 14.0.0 was not found. Microsoft.SqlServer.Types 14.0.314.76 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "libraryId": "Microsoft.SqlServer.Types", "targetGraphs": ["net9.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.ReportingServices.ReportViewerControl.Winforms 150.1652.0' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net9.0-windows7.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "libraryId": "Microsoft.ReportingServices.ReportViewerControl.Winforms", "targetGraphs": ["net9.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.SqlServer.Types 14.0.314.76' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net9.0-windows7.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\C#\\ERP\\SalesManagementSystem\\SalesManagementSystem.csproj", "libraryId": "Microsoft.SqlServer.Types", "targetGraphs": ["net9.0-windows7.0"]}]}