# نظام إدارة المبيعات - Sales Management System

## نظرة عامة
نظام إدارة المبيعات هو تطبيق Windows Forms مطور بـ C# يوفر حلاً شاملاً لإدارة المبيعات والعملاء والمنتجات مع إمكانيات التقارير المتقدمة.

## المميزات الرئيسية

### 🎨 واجهة المستخدم
- **نمط داكن عصري**: تصميم جذاب ومريح للعين
- **واجهة باللغة العربية**: دعم كامل للغة العربية مع RTL
- **تصميم متجاوب**: واجهة تتكيف مع أحجام الشاشات المختلفة

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث السريع في قاعدة بيانات العملاء
- حفظ معلومات الاتصال والعناوين

### 📦 إدارة المنتجات
- إدارة كاملة للمنتجات والمخزون
- تصنيف المنتجات حسب الفئات
- تتبع الأسعار والكميات المتاحة

### 💰 إدارة المبيعات
- تسجيل المبيعات الجديدة
- حساب الخصومات والضرائب
- تتبع تاريخ المبيعات

### 📊 التقارير والإحصائيات
- تقارير RDLC احترافية
- إحصائيات المبيعات اليومية والشهرية
- تصدير التقارير بصيغة PDF
- إمكانية الطباعة المباشرة

## التقنيات المستخدمة

### البرمجة
- **C# .NET 9.0**: لغة البرمجة الأساسية
- **Windows Forms**: لبناء واجهة المستخدم
- **SQLite**: قاعدة البيانات المحلية

### التقارير
- **Microsoft RDLC**: لإنشاء التقارير الاحترافية
- **ReportViewer**: لعرض وطباعة التقارير

### الحزم المستخدمة
- `System.Data.SQLite.Core`: للتعامل مع قاعدة بيانات SQLite
- `Microsoft.ReportingServices.ReportViewerControl.Winforms`: لعرض التقارير

## بنية المشروع

```
SalesManagementSystem/
├── Database/
│   └── DatabaseHelper.cs          # مساعد قاعدة البيانات
├── Models/
│   ├── Customer.cs                # نموذج العميل
│   ├── Product.cs                 # نموذج المنتج
│   └── Sale.cs                    # نموذج المبيعة
├── DAL/
│   ├── CustomerDAL.cs             # طبقة الوصول لبيانات العملاء
│   ├── ProductDAL.cs              # طبقة الوصول لبيانات المنتجات
│   └── SaleDAL.cs                 # طبقة الوصول لبيانات المبيعات
├── UI/
│   └── DarkTheme.cs               # نمط التصميم الداكن
├── Forms/
│   ├── MainForm.cs                # النموذج الرئيسي
│   ├── CustomersForm.cs           # نموذج إدارة العملاء
│   ├── ProductsForm.cs            # نموذج إدارة المنتجات
│   └── ReportsForm.cs             # نموذج التقارير
├── Reports/
│   └── SalesReport.rdlc           # تقرير المبيعات
└── Program.cs                     # نقطة البداية
```

## كيفية التشغيل

### المتطلبات
- Windows 10 أو أحدث
- .NET 9.0 Runtime
- 50 MB مساحة فارغة على القرص الصلب

### خطوات التشغيل
1. **استنساخ المشروع**:
   ```bash
   git clone [repository-url]
   cd SalesManagementSystem
   ```

2. **بناء المشروع**:
   ```bash
   dotnet build
   ```

3. **تشغيل التطبيق**:
   ```bash
   dotnet run
   ```

## الاستخدام

### البدء السريع
1. **تشغيل التطبيق**: سيتم إنشاء قاعدة البيانات تلقائياً مع بيانات تجريبية
2. **إدارة العملاء**: انقر على "العملاء" لإضافة أو تعديل معلومات العملاء
3. **إدارة المنتجات**: انقر على "المنتجات" لإدارة المخزون والأسعار
4. **عرض التقارير**: انقر على "التقارير" لعرض إحصائيات المبيعات

### البيانات التجريبية
يتضمن النظام بيانات تجريبية للبدء السريع:
- 3 عملاء نموذجيين
- 5 منتجات في فئات مختلفة
- إعدادات افتراضية للنظام

## المميزات المتقدمة

### النمط الداكن
- ألوان مريحة للعين
- تباين عالي للنصوص
- تصميم عصري ومتسق

### البحث والتصفية
- بحث سريع في جميع الجداول
- تصفية حسب التاريخ والفئة
- ترتيب النتائج

### التقارير
- تقارير تفاعلية مع RDLC
- تصدير PDF عالي الجودة
- طباعة مباشرة
- معاينة قبل الطباعة

## التطوير المستقبلي

### مميزات مخططة
- [ ] نظام المستخدمين والصلاحيات
- [ ] نسخ احتياطي تلقائي
- [ ] تزامن مع قواعد بيانات خارجية
- [ ] تطبيق ويب مصاحب
- [ ] تقارير إضافية (الأرباح، المخزون)
- [ ] إشعارات نفاد المخزون
- [ ] دعم العملات المتعددة

### تحسينات تقنية
- [ ] تحسين الأداء
- [ ] إضافة اختبارات وحدة
- [ ] تحسين معالجة الأخطاء
- [ ] إضافة سجل العمليات (Logging)

## المساهمة
نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم
للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة الوثائق
- التواصل مع فريق التطوير

## شكر خاص
- Microsoft لتوفير .NET Framework
- SQLite لقاعدة البيانات الموثوقة
- مجتمع المطورين العرب للدعم والمساعدة

---
**تم التطوير بـ ❤️ للمجتمع العربي**
