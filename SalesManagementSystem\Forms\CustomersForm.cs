using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using SalesManagementSystem.DAL;
using SalesManagementSystem.Models;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class CustomersForm : Form
    {
        private CustomerDAL customerDAL;
        private List<Customer> customers;
        private Customer selectedCustomer;
        private bool isEditing = false;
        
        public CustomersForm()
        {
            InitializeComponent();
            customerDAL = new CustomerDAL();
        }
        
        private void CustomersForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            DarkTheme.ApplyToForm(this);

            // تخصيص الأزرار
            SetupButtons();

            // إعداد الجدول أولاً
            SetupDataGridView();

            // ثم تحميل البيانات
            LoadCustomers();
        }
        
        private void SetupButtons()
        {
            DarkTheme.ApplyToButton(addButton);
            addButton.BackColor = DarkTheme.SuccessColor;
            
            DarkTheme.ApplyToButton(editButton);
            editButton.BackColor = DarkTheme.AccentColor;
            
            DarkTheme.ApplyToButton(deleteButton);
            deleteButton.BackColor = DarkTheme.ErrorColor;
            
            DarkTheme.ApplyToButton(refreshButton);
            DarkTheme.ApplyToButton(saveButton);
            DarkTheme.ApplyToButton(cancelButton);
            cancelButton.BackColor = DarkTheme.BorderColor;
        }
        
        private void SetupDataGridView()
        {
            DarkTheme.ApplyToDataGridView(dataGridView);
            
            dataGridView.Columns.Clear();
            dataGridView.Columns.Add("Id", "الرقم");
            dataGridView.Columns.Add("Name", "الاسم");
            dataGridView.Columns.Add("Phone", "الهاتف");
            dataGridView.Columns.Add("Email", "البريد الإلكتروني");
            dataGridView.Columns.Add("Address", "العنوان");
            dataGridView.Columns.Add("CreatedDate", "تاريخ الإنشاء");
            
            dataGridView.Columns["Id"].Width = 80;
            dataGridView.Columns["Name"].Width = 150;
            dataGridView.Columns["Phone"].Width = 120;
            dataGridView.Columns["Email"].Width = 180;
            dataGridView.Columns["Address"].Width = 200;
            dataGridView.Columns["CreatedDate"].Width = 120;
            
            dataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "dd/MM/yyyy";
        }
        
        private void LoadCustomers()
        {
            try
            {
                customers = customerDAL.GetAllCustomers();
                DisplayCustomers(customers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void DisplayCustomers(List<Customer> customersToDisplay)
        {
            dataGridView.Rows.Clear();
            
            foreach (var customer in customersToDisplay)
            {
                dataGridView.Rows.Add(
                    customer.Id,
                    customer.Name,
                    customer.Phone,
                    customer.Email,
                    customer.Address,
                    customer.CreatedDate
                );
            }
            
            UpdateButtonStates();
        }
        
        private void UpdateButtonStates()
        {
            bool hasSelection = dataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }
        
        private void ShowForm(bool show)
        {
            formPanel.Visible = show;
            if (show)
            {
                controlPanel.Height = 200;
            }
            else
            {
                controlPanel.Height = 150;
                ClearForm();
            }
        }
        
        private void ClearForm()
        {
            nameTextBox.Clear();
            phoneTextBox.Clear();
            emailTextBox.Clear();
            addressTextBox.Clear();
            selectedCustomer = null;
            isEditing = false;
        }
        
        private void LoadCustomerToForm(Customer customer)
        {
            nameTextBox.Text = customer.Name;
            phoneTextBox.Text = customer.Phone;
            emailTextBox.Text = customer.Email;
            addressTextBox.Text = customer.Address;
            selectedCustomer = customer;
        }
        
        private Customer GetCustomerFromForm()
        {
            return new Customer
            {
                Id = selectedCustomer?.Id ?? 0,
                Name = nameTextBox.Text.Trim(),
                Phone = phoneTextBox.Text.Trim(),
                Email = emailTextBox.Text.Trim(),
                Address = addressTextBox.Text.Trim()
            };
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }
            
            return true;
        }
        
        // أحداث الأزرار
        private void addButton_Click(object sender, EventArgs e)
        {
            var addCustomerForm = new AddCustomerForm();
            if (addCustomerForm.ShowDialog() == DialogResult.OK)
            {
                LoadCustomers(); // تحديث القائمة بعد الإضافة
            }
        }
        
        private void editButton_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0) return;

            int customerId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
            var customer = customers.FirstOrDefault(c => c.Id == customerId);

            if (customer != null)
            {
                var editCustomerForm = new AddCustomerForm(customer);
                if (editCustomerForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCustomers(); // تحديث القائمة بعد التعديل
                }
            }
        }
        
        private void deleteButton_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0) return;
            
            int customerId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
            string customerName = dataGridView.SelectedRows[0].Cells["Name"].Value.ToString();
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف العميل '{customerName}'؟", 
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    if (customerDAL.DeleteCustomer(customerId))
                    {
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف العميل", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private void refreshButton_Click(object sender, EventArgs e)
        {
            LoadCustomers();
            ShowForm(false);
        }
        
        private void saveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;
            
            try
            {
                var customer = GetCustomerFromForm();
                
                if (isEditing)
                {
                    if (customerDAL.UpdateCustomer(customer))
                    {
                        MessageBox.Show("تم تحديث العميل بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث العميل", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                else
                {
                    int newId = customerDAL.AddCustomer(customer);
                    if (newId > 0)
                    {
                        MessageBox.Show("تم إضافة العميل بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة العميل", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                
                LoadCustomers();
                ShowForm(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void cancelButton_Click(object sender, EventArgs e)
        {
            ShowForm(false);
        }
        
        private void searchTextBox_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = searchTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                DisplayCustomers(customers);
            }
            else
            {
                try
                {
                    var filteredCustomers = customerDAL.SearchCustomers(searchTerm);
                    DisplayCustomers(filteredCustomers);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }
    }
}
