using System;
using System.Collections.Generic;
using System.Data.SQLite;
using SalesManagementSystem.Database;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.DAL
{
    public class CustomerDAL
    {
        public List<Customer> GetAllCustomers()
        {
            List<Customer> customers = new List<Customer>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Customers ORDER BY Name";
                
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        customers.Add(new Customer
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            Name = reader["Name"].ToString(),
                            Phone = reader["Phone"].ToString(),
                            Email = reader["Email"].ToString(),
                            Address = reader["Address"].ToString(),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                        });
                    }
                }
            }
            
            return customers;
        }
        
        public Customer GetCustomerById(int id)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Customers WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Customer
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                Name = reader["Name"].ToString(),
                                Phone = reader["Phone"].ToString(),
                                Email = reader["Email"].ToString(),
                                Address = reader["Address"].ToString(),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        public int AddCustomer(Customer customer)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"INSERT INTO Customers (Name, Phone, Email, Address) 
                               VALUES (@Name, @Phone, @Email, @Address);
                               SELECT last_insert_rowid();";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", customer.Name);
                    command.Parameters.AddWithValue("@Phone", customer.Phone ?? "");
                    command.Parameters.AddWithValue("@Email", customer.Email ?? "");
                    command.Parameters.AddWithValue("@Address", customer.Address ?? "");
                    
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }
        
        public bool UpdateCustomer(Customer customer)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"UPDATE Customers 
                               SET Name = @Name, Phone = @Phone, Email = @Email, Address = @Address 
                               WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", customer.Id);
                    command.Parameters.AddWithValue("@Name", customer.Name);
                    command.Parameters.AddWithValue("@Phone", customer.Phone ?? "");
                    command.Parameters.AddWithValue("@Email", customer.Email ?? "");
                    command.Parameters.AddWithValue("@Address", customer.Address ?? "");
                    
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }
        
        public bool DeleteCustomer(int id)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "DELETE FROM Customers WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }
        
        public List<Customer> SearchCustomers(string searchTerm)
        {
            List<Customer> customers = new List<Customer>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"SELECT * FROM Customers 
                               WHERE Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm
                               ORDER BY Name";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(new Customer
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                Name = reader["Name"].ToString(),
                                Phone = reader["Phone"].ToString(),
                                Email = reader["Email"].ToString(),
                                Address = reader["Address"].ToString(),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                            });
                        }
                    }
                }
            }
            
            return customers;
        }
    }
}
