using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using SalesManagementSystem.DAL;
using SalesManagementSystem.Models;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class ProductsForm : Form
    {
        private ProductDAL productDAL;
        private List<Product> products;
        private Product selectedProduct;
        private bool isEditing = false;
        
        public ProductsForm()
        {
            InitializeComponent();
            productDAL = new ProductDAL();
        }
        
        private void ProductsForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            DarkTheme.ApplyToForm(this);

            // تخصيص الأزرار
            SetupButtons();

            // إعداد الجدول أولاً
            SetupDataGridView();

            // ثم تحميل البيانات
            LoadProducts();
        }
        
        private void SetupButtons()
        {
            DarkTheme.ApplyToButton(addButton);
            addButton.BackColor = DarkTheme.SuccessColor;
            
            DarkTheme.ApplyToButton(editButton);
            editButton.BackColor = DarkTheme.AccentColor;
            
            DarkTheme.ApplyToButton(deleteButton);
            deleteButton.BackColor = DarkTheme.ErrorColor;
            
            DarkTheme.ApplyToButton(refreshButton);
            DarkTheme.ApplyToButton(saveButton);
            DarkTheme.ApplyToButton(cancelButton);
            cancelButton.BackColor = DarkTheme.BorderColor;
        }
        
        private void SetupDataGridView()
        {
            DarkTheme.ApplyToDataGridView(dataGridView);
            
            dataGridView.Columns.Clear();
            dataGridView.Columns.Add("Id", "الرقم");
            dataGridView.Columns.Add("Name", "الاسم");
            dataGridView.Columns.Add("Description", "الوصف");
            dataGridView.Columns.Add("Price", "السعر");
            dataGridView.Columns.Add("Stock", "المخزون");
            dataGridView.Columns.Add("Category", "الفئة");
            dataGridView.Columns.Add("CreatedDate", "تاريخ الإنشاء");
            
            dataGridView.Columns["Id"].Width = 80;
            dataGridView.Columns["Name"].Width = 150;
            dataGridView.Columns["Description"].Width = 200;
            dataGridView.Columns["Price"].Width = 100;
            dataGridView.Columns["Stock"].Width = 80;
            dataGridView.Columns["Category"].Width = 120;
            dataGridView.Columns["CreatedDate"].Width = 120;
            
            dataGridView.Columns["Price"].DefaultCellStyle.Format = "C2";
            dataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "dd/MM/yyyy";
        }
        
        private void LoadProducts()
        {
            try
            {
                products = productDAL.GetAllProducts();
                DisplayProducts(products);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void DisplayProducts(List<Product> productsToDisplay)
        {
            dataGridView.Rows.Clear();
            
            foreach (var product in productsToDisplay)
            {
                dataGridView.Rows.Add(
                    product.Id,
                    product.Name,
                    product.Description,
                    product.Price,
                    product.Stock,
                    product.Category,
                    product.CreatedDate
                );
            }
            
            UpdateButtonStates();
        }
        
        private void UpdateButtonStates()
        {
            bool hasSelection = dataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }
        
        private void ShowForm(bool show)
        {
            formPanel.Visible = show;
            if (show)
            {
                controlPanel.Height = 220;
            }
            else
            {
                controlPanel.Height = 150;
                ClearForm();
            }
        }
        
        private void ClearForm()
        {
            nameTextBox.Clear();
            descriptionTextBox.Clear();
            priceTextBox.Clear();
            stockTextBox.Clear();
            categoryTextBox.Clear();
            selectedProduct = null;
            isEditing = false;
        }
        
        private void LoadProductToForm(Product product)
        {
            nameTextBox.Text = product.Name;
            descriptionTextBox.Text = product.Description;
            priceTextBox.Text = product.Price.ToString("F2");
            stockTextBox.Text = product.Stock.ToString();
            categoryTextBox.Text = product.Category;
            selectedProduct = product;
        }
        
        private Product GetProductFromForm()
        {
            return new Product
            {
                Id = selectedProduct?.Id ?? 0,
                Name = nameTextBox.Text.Trim(),
                Description = descriptionTextBox.Text.Trim(),
                Price = decimal.TryParse(priceTextBox.Text, out decimal price) ? price : 0,
                Stock = int.TryParse(stockTextBox.Text, out int stock) ? stock : 0,
                Category = categoryTextBox.Text.Trim()
            };
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(priceTextBox.Text, out decimal price) || price < 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                priceTextBox.Focus();
                return false;
            }
            
            if (!int.TryParse(stockTextBox.Text, out int stock) || stock < 0)
            {
                MessageBox.Show("يرجى إدخال كمية مخزون صحيحة", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                stockTextBox.Focus();
                return false;
            }
            
            return true;
        }
        
        // أحداث الأزرار
        private void addButton_Click(object sender, EventArgs e)
        {
            var addProductForm = new AddProductForm();
            if (addProductForm.ShowDialog() == DialogResult.OK)
            {
                LoadProducts(); // تحديث القائمة بعد الإضافة
            }
        }
        
        private void editButton_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0) return;

            int productId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
            var product = products.FirstOrDefault(p => p.Id == productId);

            if (product != null)
            {
                var editProductForm = new AddProductForm(product);
                if (editProductForm.ShowDialog() == DialogResult.OK)
                {
                    LoadProducts(); // تحديث القائمة بعد التعديل
                }
            }
        }
        
        private void deleteButton_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0) return;
            
            int productId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
            string productName = dataGridView.SelectedRows[0].Cells["Name"].Value.ToString();
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف المنتج '{productName}'؟", 
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    if (productDAL.DeleteProduct(productId))
                    {
                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private void refreshButton_Click(object sender, EventArgs e)
        {
            LoadProducts();
            ShowForm(false);
        }
        
        private void saveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;
            
            try
            {
                var product = GetProductFromForm();
                
                if (isEditing)
                {
                    if (productDAL.UpdateProduct(product))
                    {
                        MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                else
                {
                    int newId = productDAL.AddProduct(product);
                    if (newId > 0)
                    {
                        MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                
                LoadProducts();
                ShowForm(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void cancelButton_Click(object sender, EventArgs e)
        {
            ShowForm(false);
        }
        
        private void searchTextBox_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = searchTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                DisplayProducts(products);
            }
            else
            {
                try
                {
                    var filteredProducts = productDAL.SearchProducts(searchTerm);
                    DisplayProducts(filteredProducts);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }
    }
}
