using SalesManagementSystem.UI;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    partial class CustomersForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.dataGridView = new DataGridView();
            this.controlPanel = new Panel();
            this.searchPanel = new Panel();
            this.searchTextBox = new TextBox();
            this.searchLabel = new Label();
            this.buttonPanel = new Panel();
            this.addButton = new Button();
            this.editButton = new Button();
            this.deleteButton = new Button();
            this.refreshButton = new Button();
            this.formPanel = new Panel();
            this.nameTextBox = new TextBox();
            this.nameLabel = new Label();
            this.phoneTextBox = new TextBox();
            this.phoneLabel = new Label();
            this.emailTextBox = new TextBox();
            this.emailLabel = new Label();
            this.addressTextBox = new TextBox();
            this.addressLabel = new Label();
            this.saveButton = new Button();
            this.cancelButton = new Button();
            this.titleLabel = new Label();
            
            this.mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.controlPanel.SuspendLayout();
            this.searchPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.formPanel.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // mainPanel
            // 
            this.mainPanel.Controls.Add(this.dataGridView);
            this.mainPanel.Controls.Add(this.controlPanel);
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Location = new Point(0, 0);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Padding = new Padding(20);
            this.mainPanel.Size = new Size(1000, 600);
            this.mainPanel.TabIndex = 0;
            
            // 
            // controlPanel
            // 
            this.controlPanel.Controls.Add(this.formPanel);
            this.controlPanel.Controls.Add(this.buttonPanel);
            this.controlPanel.Controls.Add(this.searchPanel);
            this.controlPanel.Controls.Add(this.titleLabel);
            this.controlPanel.Dock = DockStyle.Top;
            this.controlPanel.Location = new Point(20, 20);
            this.controlPanel.Name = "controlPanel";
            this.controlPanel.Size = new Size(960, 200);
            this.controlPanel.TabIndex = 0;
            
            // 
            // titleLabel
            // 
            this.titleLabel.Dock = DockStyle.Top;
            this.titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.titleLabel.Location = new Point(0, 0);
            this.titleLabel.Name = "titleLabel";
            this.titleLabel.Size = new Size(960, 40);
            this.titleLabel.TabIndex = 0;
            this.titleLabel.Text = "إدارة العملاء";
            this.titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // 
            // searchPanel
            // 
            this.searchPanel.Controls.Add(this.searchTextBox);
            this.searchPanel.Controls.Add(this.searchLabel);
            this.searchPanel.Dock = DockStyle.Top;
            this.searchPanel.Location = new Point(0, 40);
            this.searchPanel.Name = "searchPanel";
            this.searchPanel.Padding = new Padding(10);
            this.searchPanel.Size = new Size(960, 50);
            this.searchPanel.TabIndex = 1;
            
            // 
            // searchLabel
            // 
            this.searchLabel.AutoSize = true;
            this.searchLabel.Location = new Point(10, 18);
            this.searchLabel.Name = "searchLabel";
            this.searchLabel.Size = new Size(35, 15);
            this.searchLabel.TabIndex = 0;
            this.searchLabel.Text = "بحث:";
            
            // 
            // searchTextBox
            // 
            this.searchTextBox.Location = new Point(60, 15);
            this.searchTextBox.Name = "searchTextBox";
            this.searchTextBox.PlaceholderText = "ابحث عن عميل...";
            this.searchTextBox.Size = new Size(300, 23);
            this.searchTextBox.TabIndex = 1;
            this.searchTextBox.TextChanged += new System.EventHandler(this.searchTextBox_TextChanged);
            
            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.refreshButton);
            this.buttonPanel.Controls.Add(this.deleteButton);
            this.buttonPanel.Controls.Add(this.editButton);
            this.buttonPanel.Controls.Add(this.addButton);
            this.buttonPanel.Dock = DockStyle.Top;
            this.buttonPanel.Location = new Point(0, 90);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Padding = new Padding(10);
            this.buttonPanel.Size = new Size(960, 60);
            this.buttonPanel.TabIndex = 2;
            
            // 
            // addButton
            // 
            this.addButton.Location = new Point(10, 15);
            this.addButton.Name = "addButton";
            this.addButton.Size = new Size(100, 35);
            this.addButton.TabIndex = 0;
            this.addButton.Text = "إضافة";
            this.addButton.UseVisualStyleBackColor = true;
            this.addButton.Click += new System.EventHandler(this.addButton_Click);
            
            // 
            // editButton
            // 
            this.editButton.Location = new Point(120, 15);
            this.editButton.Name = "editButton";
            this.editButton.Size = new Size(100, 35);
            this.editButton.TabIndex = 1;
            this.editButton.Text = "تعديل";
            this.editButton.UseVisualStyleBackColor = true;
            this.editButton.Click += new System.EventHandler(this.editButton_Click);
            
            // 
            // deleteButton
            // 
            this.deleteButton.Location = new Point(230, 15);
            this.deleteButton.Name = "deleteButton";
            this.deleteButton.Size = new Size(100, 35);
            this.deleteButton.TabIndex = 2;
            this.deleteButton.Text = "حذف";
            this.deleteButton.UseVisualStyleBackColor = true;
            this.deleteButton.Click += new System.EventHandler(this.deleteButton_Click);
            
            // 
            // refreshButton
            // 
            this.refreshButton.Location = new Point(340, 15);
            this.refreshButton.Name = "refreshButton";
            this.refreshButton.Size = new Size(100, 35);
            this.refreshButton.TabIndex = 3;
            this.refreshButton.Text = "تحديث";
            this.refreshButton.UseVisualStyleBackColor = true;
            this.refreshButton.Click += new System.EventHandler(this.refreshButton_Click);
            
            // 
            // formPanel
            // 
            this.formPanel.Controls.Add(this.cancelButton);
            this.formPanel.Controls.Add(this.saveButton);
            this.formPanel.Controls.Add(this.addressTextBox);
            this.formPanel.Controls.Add(this.addressLabel);
            this.formPanel.Controls.Add(this.emailTextBox);
            this.formPanel.Controls.Add(this.emailLabel);
            this.formPanel.Controls.Add(this.phoneTextBox);
            this.formPanel.Controls.Add(this.phoneLabel);
            this.formPanel.Controls.Add(this.nameTextBox);
            this.formPanel.Controls.Add(this.nameLabel);
            this.formPanel.Dock = DockStyle.Fill;
            this.formPanel.Location = new Point(0, 150);
            this.formPanel.Name = "formPanel";
            this.formPanel.Padding = new Padding(10);
            this.formPanel.Size = new Size(960, 50);
            this.formPanel.TabIndex = 3;
            this.formPanel.Visible = false;
            
            // 
            // nameLabel
            // 
            this.nameLabel.AutoSize = true;
            this.nameLabel.Location = new Point(10, 15);
            this.nameLabel.Name = "nameLabel";
            this.nameLabel.Size = new Size(35, 15);
            this.nameLabel.TabIndex = 0;
            this.nameLabel.Text = "الاسم:";
            
            // 
            // nameTextBox
            // 
            this.nameTextBox.Location = new Point(60, 12);
            this.nameTextBox.Name = "nameTextBox";
            this.nameTextBox.Size = new Size(200, 23);
            this.nameTextBox.TabIndex = 1;
            
            // 
            // phoneLabel
            // 
            this.phoneLabel.AutoSize = true;
            this.phoneLabel.Location = new Point(280, 15);
            this.phoneLabel.Name = "phoneLabel";
            this.phoneLabel.Size = new Size(40, 15);
            this.phoneLabel.TabIndex = 2;
            this.phoneLabel.Text = "الهاتف:";
            
            // 
            // phoneTextBox
            // 
            this.phoneTextBox.Location = new Point(330, 12);
            this.phoneTextBox.Name = "phoneTextBox";
            this.phoneTextBox.Size = new Size(150, 23);
            this.phoneTextBox.TabIndex = 3;
            
            // 
            // emailLabel
            // 
            this.emailLabel.AutoSize = true;
            this.emailLabel.Location = new Point(500, 15);
            this.emailLabel.Name = "emailLabel";
            this.emailLabel.Size = new Size(80, 15);
            this.emailLabel.TabIndex = 4;
            this.emailLabel.Text = "البريد الإلكتروني:";
            
            // 
            // emailTextBox
            // 
            this.emailTextBox.Location = new Point(590, 12);
            this.emailTextBox.Name = "emailTextBox";
            this.emailTextBox.Size = new Size(200, 23);
            this.emailTextBox.TabIndex = 5;
            
            // 
            // addressLabel
            // 
            this.addressLabel.AutoSize = true;
            this.addressLabel.Location = new Point(10, 50);
            this.addressLabel.Name = "addressLabel";
            this.addressLabel.Size = new Size(40, 15);
            this.addressLabel.TabIndex = 6;
            this.addressLabel.Text = "العنوان:";
            
            // 
            // addressTextBox
            // 
            this.addressTextBox.Location = new Point(60, 47);
            this.addressTextBox.Multiline = true;
            this.addressTextBox.Name = "addressTextBox";
            this.addressTextBox.Size = new Size(730, 60);
            this.addressTextBox.TabIndex = 7;
            
            // 
            // saveButton
            // 
            this.saveButton.Location = new Point(10, 120);
            this.saveButton.Name = "saveButton";
            this.saveButton.Size = new Size(100, 35);
            this.saveButton.TabIndex = 8;
            this.saveButton.Text = "حفظ";
            this.saveButton.UseVisualStyleBackColor = true;
            this.saveButton.Click += new System.EventHandler(this.saveButton_Click);
            
            // 
            // cancelButton
            // 
            this.cancelButton.Location = new Point(120, 120);
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.Size = new Size(100, 35);
            this.cancelButton.TabIndex = 9;
            this.cancelButton.Text = "إلغاء";
            this.cancelButton.UseVisualStyleBackColor = true;
            this.cancelButton.Click += new System.EventHandler(this.cancelButton_Click);
            
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Dock = DockStyle.Fill;
            this.dataGridView.Location = new Point(20, 220);
            this.dataGridView.MultiSelect = false;
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new Size(960, 360);
            this.dataGridView.TabIndex = 1;
            this.dataGridView.SelectionChanged += new System.EventHandler(this.dataGridView_SelectionChanged);
            
            // 
            // CustomersForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 600);
            this.Controls.Add(this.mainPanel);
            this.Name = "CustomersForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "إدارة العملاء";
            this.Load += new System.EventHandler(this.CustomersForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.controlPanel.ResumeLayout(false);
            this.searchPanel.ResumeLayout(false);
            this.searchPanel.PerformLayout();
            this.buttonPanel.ResumeLayout(false);
            this.formPanel.ResumeLayout(false);
            this.formPanel.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Panel mainPanel;
        private Panel controlPanel;
        private Label titleLabel;
        private Panel searchPanel;
        private Label searchLabel;
        private TextBox searchTextBox;
        private Panel buttonPanel;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private Panel formPanel;
        private Label nameLabel;
        private TextBox nameTextBox;
        private Label phoneLabel;
        private TextBox phoneTextBox;
        private Label emailLabel;
        private TextBox emailTextBox;
        private Label addressLabel;
        private TextBox addressTextBox;
        private Button saveButton;
        private Button cancelButton;
        private DataGridView dataGridView;
    }
}
