using System;
using System.Windows.Forms;
using SalesManagementSystem.DAL;
using SalesManagementSystem.Models;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class AddCustomerForm : Form
    {
        private CustomerDAL customerDAL;
        private Customer? customerToEdit;
        private bool isEditMode = false;
        
        public AddCustomerForm()
        {
            InitializeComponent();
            customerDAL = new CustomerDAL();
        }
        
        public AddCustomerForm(Customer customer) : this()
        {
            customerToEdit = customer;
            isEditMode = true;
        }
        
        private void AddCustomerForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            ApplyDarkTheme();
            
            // إعداد النموذج
            SetupForm();
            
            // إذا كان في وضع التعديل، تحميل بيانات العميل
            if (isEditMode && customerToEdit != null)
            {
                LoadCustomerData();
            }
            
            // التركيز على حقل الاسم
            nameTextBox.Focus();
        }
        
        private void ApplyDarkTheme()
        {
            // تطبيق النمط الداكن على النموذج
            this.BackColor = DarkTheme.BackgroundColor;
            this.ForeColor = DarkTheme.TextColor;
            
            // الشريط العلوي
            headerPanel.BackColor = DarkTheme.PrimaryColor;
            titleLabel.ForeColor = Color.White;
            
            // زر الإغلاق
            closeButton.BackColor = DarkTheme.ErrorColor;
            closeButton.ForeColor = Color.White;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(200, 50, 50);
            
            // البطاقة الرئيسية
            formCard.BackColor = DarkTheme.CardColor;
            
            // حقول الإدخال
            ApplyTextBoxTheme(nameTextBox);
            ApplyTextBoxTheme(phoneTextBox);
            ApplyTextBoxTheme(emailTextBox);
            ApplyTextBoxTheme(addressTextBox);
            
            // التسميات
            nameLabel.ForeColor = DarkTheme.TextColor;
            phoneLabel.ForeColor = DarkTheme.TextColor;
            emailLabel.ForeColor = DarkTheme.TextColor;
            addressLabel.ForeColor = DarkTheme.TextColor;
            
            // الأزرار
            SetupButtons();
        }
        
        private void ApplyTextBoxTheme(TextBox textBox)
        {
            textBox.BackColor = DarkTheme.SurfaceColor;
            textBox.ForeColor = DarkTheme.TextColor;
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }
        
        private void SetupButtons()
        {
            // زر الحفظ
            saveButton.BackColor = DarkTheme.SuccessColor;
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(40, 180, 70);
            
            // زر الإلغاء
            cancelButton.BackColor = DarkTheme.BorderColor;
            cancelButton.ForeColor = Color.White;
            cancelButton.FlatStyle = FlatStyle.Flat;
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(100, 100, 100);
            
            // زر المسح
            clearButton.BackColor = DarkTheme.AccentColor;
            clearButton.ForeColor = Color.White;
            clearButton.FlatStyle = FlatStyle.Flat;
            clearButton.FlatAppearance.BorderSize = 0;
            clearButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 140, 0);
        }
        
        private void SetupForm()
        {
            // تحديث العنوان حسب الوضع
            if (isEditMode)
            {
                titleLabel.Text = "تعديل بيانات العميل";
                saveButton.Text = "💾 تحديث";
            }
            else
            {
                titleLabel.Text = "إضافة عميل جديد";
                saveButton.Text = "💾 حفظ";
            }
            
            // إضافة تأثير الظل للنموذج
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.Magenta;
            this.TransparencyKey = Color.Magenta;
            
            // إضافة حدود للبطاقة
            formCard.BorderStyle = BorderStyle.FixedSingle;
        }
        
        private void LoadCustomerData()
        {
            if (customerToEdit != null)
            {
                nameTextBox.Text = customerToEdit.Name;
                phoneTextBox.Text = customerToEdit.Phone;
                emailTextBox.Text = customerToEdit.Email;
                addressTextBox.Text = customerToEdit.Address;
            }
        }
        
        private void ClearForm()
        {
            nameTextBox.Clear();
            phoneTextBox.Clear();
            emailTextBox.Clear();
            addressTextBox.Clear();
            nameTextBox.Focus();
        }
        
        private bool ValidateForm()
        {
            // التحقق من الاسم (مطلوب)
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }
            
            // التحقق من صحة البريد الإلكتروني (إذا تم إدخاله)
            if (!string.IsNullOrWhiteSpace(emailTextBox.Text))
            {
                if (!IsValidEmail(emailTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "خطأ في البيانات", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    emailTextBox.Focus();
                    return false;
                }
            }
            
            return true;
        }
        
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        
        private Customer GetCustomerFromForm()
        {
            return new Customer
            {
                Id = isEditMode && customerToEdit != null ? customerToEdit.Id : 0,
                Name = nameTextBox.Text.Trim(),
                Phone = phoneTextBox.Text.Trim(),
                Email = emailTextBox.Text.Trim(),
                Address = addressTextBox.Text.Trim()
            };
        }
        
        // أحداث الأزرار
        private void saveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;
            
            try
            {
                var customer = GetCustomerFromForm();
                
                if (isEditMode)
                {
                    // تحديث العميل
                    if (customerDAL.UpdateCustomer(customer))
                    {
                        MessageBox.Show("تم تحديث بيانات العميل بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات العميل", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    // إضافة عميل جديد
                    int newId = customerDAL.AddCustomer(customer);
                    if (newId > 0)
                    {
                        MessageBox.Show("تم إضافة العميل بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        // سؤال المستخدم إذا كان يريد إضافة عميل آخر
                        var result = MessageBox.Show("هل تريد إضافة عميل آخر؟", "إضافة عميل آخر", 
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        
                        if (result == DialogResult.Yes)
                        {
                            ClearForm();
                        }
                        else
                        {
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة العميل", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void cancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        private void clearButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من مسح جميع البيانات؟", "تأكيد المسح", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                ClearForm();
            }
        }
        
        private void closeButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        // إضافة تأثيرات بصرية
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            // رسم ظل للنموذج
            var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0));
            e.Graphics.FillRectangle(shadowBrush, 5, 5, this.Width - 5, this.Height - 5);
            shadowBrush.Dispose();
        }
    }
}
