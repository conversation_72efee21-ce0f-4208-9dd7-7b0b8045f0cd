using SalesManagementSystem.UI;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.fileMenuItem = new ToolStripMenuItem();
            this.exitMenuItem = new ToolStripMenuItem();
            this.salesMenuItem = new ToolStripMenuItem();
            this.newSaleMenuItem = new ToolStripMenuItem();
            this.viewSalesMenuItem = new ToolStripMenuItem();
            this.customersMenuItem = new ToolStripMenuItem();
            this.manageCustomersMenuItem = new ToolStripMenuItem();
            this.productsMenuItem = new ToolStripMenuItem();
            this.manageProductsMenuItem = new ToolStripMenuItem();
            this.reportsMenuItem = new ToolStripMenuItem();
            this.salesReportMenuItem = new ToolStripMenuItem();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.mainPanel = new Panel();
            this.sidePanel = new Panel();
            this.logoPanel = new Panel();
            this.logoLabel = new Label();
            this.dashboardButton = new Button();
            this.salesButton = new Button();
            this.customersButton = new Button();
            this.productsButton = new Button();
            this.reportsButton = new Button();
            this.contentPanel = new Panel();
            this.welcomeLabel = new Label();
            this.statsPanel = new Panel();
            this.todaySalesCard = new Panel();
            this.todaySalesLabel = new Label();
            this.todaySalesValue = new Label();
            this.totalCustomersCard = new Panel();
            this.totalCustomersLabel = new Label();
            this.totalCustomersValue = new Label();
            this.totalProductsCard = new Panel();
            this.totalProductsLabel = new Label();
            this.totalProductsValue = new Label();
            this.monthSalesCard = new Panel();
            this.monthSalesLabel = new Label();
            this.monthSalesValue = new Label();
            
            this.menuStrip.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.mainPanel.SuspendLayout();
            this.sidePanel.SuspendLayout();
            this.logoPanel.SuspendLayout();
            this.contentPanel.SuspendLayout();
            this.statsPanel.SuspendLayout();
            this.todaySalesCard.SuspendLayout();
            this.totalCustomersCard.SuspendLayout();
            this.totalProductsCard.SuspendLayout();
            this.monthSalesCard.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // menuStrip
            // 
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                this.fileMenuItem,
                this.salesMenuItem,
                this.customersMenuItem,
                this.productsMenuItem,
                this.reportsMenuItem});
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new Size(1200, 24);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.Text = "menuStrip";
            
            // 
            // fileMenuItem
            // 
            this.fileMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.exitMenuItem});
            this.fileMenuItem.Name = "fileMenuItem";
            this.fileMenuItem.Size = new Size(37, 20);
            this.fileMenuItem.Text = "ملف";
            
            // 
            // exitMenuItem
            // 
            this.exitMenuItem.Name = "exitMenuItem";
            this.exitMenuItem.Size = new Size(100, 22);
            this.exitMenuItem.Text = "خروج";
            this.exitMenuItem.Click += new System.EventHandler(this.exitMenuItem_Click);
            
            // 
            // salesMenuItem
            // 
            this.salesMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.newSaleMenuItem,
                this.viewSalesMenuItem});
            this.salesMenuItem.Name = "salesMenuItem";
            this.salesMenuItem.Size = new Size(56, 20);
            this.salesMenuItem.Text = "المبيعات";
            
            // 
            // newSaleMenuItem
            // 
            this.newSaleMenuItem.Name = "newSaleMenuItem";
            this.newSaleMenuItem.Size = new Size(140, 22);
            this.newSaleMenuItem.Text = "مبيعة جديدة";
            this.newSaleMenuItem.Click += new System.EventHandler(this.newSaleMenuItem_Click);
            
            // 
            // viewSalesMenuItem
            // 
            this.viewSalesMenuItem.Name = "viewSalesMenuItem";
            this.viewSalesMenuItem.Size = new Size(140, 22);
            this.viewSalesMenuItem.Text = "عرض المبيعات";
            this.viewSalesMenuItem.Click += new System.EventHandler(this.viewSalesMenuItem_Click);
            
            // 
            // customersMenuItem
            // 
            this.customersMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.manageCustomersMenuItem});
            this.customersMenuItem.Name = "customersMenuItem";
            this.customersMenuItem.Size = new Size(50, 20);
            this.customersMenuItem.Text = "العملاء";
            
            // 
            // manageCustomersMenuItem
            // 
            this.manageCustomersMenuItem.Name = "manageCustomersMenuItem";
            this.manageCustomersMenuItem.Size = new Size(130, 22);
            this.manageCustomersMenuItem.Text = "إدارة العملاء";
            this.manageCustomersMenuItem.Click += new System.EventHandler(this.manageCustomersMenuItem_Click);
            
            // 
            // productsMenuItem
            // 
            this.productsMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.manageProductsMenuItem});
            this.productsMenuItem.Name = "productsMenuItem";
            this.productsMenuItem.Size = new Size(58, 20);
            this.productsMenuItem.Text = "المنتجات";
            
            // 
            // manageProductsMenuItem
            // 
            this.manageProductsMenuItem.Name = "manageProductsMenuItem";
            this.manageProductsMenuItem.Size = new Size(140, 22);
            this.manageProductsMenuItem.Text = "إدارة المنتجات";
            this.manageProductsMenuItem.Click += new System.EventHandler(this.manageProductsMenuItem_Click);
            
            // 
            // reportsMenuItem
            // 
            this.reportsMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.salesReportMenuItem});
            this.reportsMenuItem.Name = "reportsMenuItem";
            this.reportsMenuItem.Size = new Size(53, 20);
            this.reportsMenuItem.Text = "التقارير";
            
            // 
            // salesReportMenuItem
            // 
            this.salesReportMenuItem.Name = "salesReportMenuItem";
            this.salesReportMenuItem.Size = new Size(130, 22);
            this.salesReportMenuItem.Text = "تقرير المبيعات";
            this.salesReportMenuItem.Click += new System.EventHandler(this.salesReportMenuItem_Click);
            
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel});
            this.statusStrip.Location = new Point(0, 676);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1200, 24);
            this.statusStrip.TabIndex = 1;
            this.statusStrip.Text = "statusStrip";
            
            // 
            // statusLabel
            // 
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new Size(118, 19);
            this.statusLabel.Text = "جاهز - نظام إدارة المبيعات";
            
            // 
            // mainPanel
            // 
            this.mainPanel.Controls.Add(this.contentPanel);
            this.mainPanel.Controls.Add(this.sidePanel);
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Location = new Point(0, 24);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Size = new Size(1200, 652);
            this.mainPanel.TabIndex = 2;
            
            // 
            // sidePanel
            // 
            this.sidePanel.Controls.Add(this.reportsButton);
            this.sidePanel.Controls.Add(this.productsButton);
            this.sidePanel.Controls.Add(this.customersButton);
            this.sidePanel.Controls.Add(this.salesButton);
            this.sidePanel.Controls.Add(this.dashboardButton);
            this.sidePanel.Controls.Add(this.logoPanel);
            this.sidePanel.Dock = DockStyle.Right;
            this.sidePanel.Location = new Point(1000, 0);
            this.sidePanel.Name = "sidePanel";
            this.sidePanel.Size = new Size(200, 652);
            this.sidePanel.TabIndex = 0;
            
            // 
            // logoPanel
            // 
            this.logoPanel.Controls.Add(this.logoLabel);
            this.logoPanel.Dock = DockStyle.Top;
            this.logoPanel.Location = new Point(0, 0);
            this.logoPanel.Name = "logoPanel";
            this.logoPanel.Size = new Size(200, 80);
            this.logoPanel.TabIndex = 0;
            
            // 
            // logoLabel
            // 
            this.logoLabel.Dock = DockStyle.Fill;
            this.logoLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.logoLabel.Location = new Point(0, 0);
            this.logoLabel.Name = "logoLabel";
            this.logoLabel.Size = new Size(200, 80);
            this.logoLabel.TabIndex = 0;
            this.logoLabel.Text = "نظام إدارة المبيعات";
            this.logoLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // dashboardButton
            //
            this.dashboardButton.Dock = DockStyle.Top;
            this.dashboardButton.FlatStyle = FlatStyle.Flat;
            this.dashboardButton.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            this.dashboardButton.Location = new Point(0, 80);
            this.dashboardButton.Name = "dashboardButton";
            this.dashboardButton.Size = new Size(200, 50);
            this.dashboardButton.TabIndex = 1;
            this.dashboardButton.Text = "🏠 لوحة التحكم";
            this.dashboardButton.UseVisualStyleBackColor = true;
            this.dashboardButton.Click += new System.EventHandler(this.dashboardButton_Click);

            //
            // salesButton
            //
            this.salesButton.Dock = DockStyle.Top;
            this.salesButton.FlatStyle = FlatStyle.Flat;
            this.salesButton.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            this.salesButton.Location = new Point(0, 130);
            this.salesButton.Name = "salesButton";
            this.salesButton.Size = new Size(200, 50);
            this.salesButton.TabIndex = 2;
            this.salesButton.Text = "💰 المبيعات";
            this.salesButton.UseVisualStyleBackColor = true;
            this.salesButton.Click += new System.EventHandler(this.salesButton_Click);

            //
            // customersButton
            //
            this.customersButton.Dock = DockStyle.Top;
            this.customersButton.FlatStyle = FlatStyle.Flat;
            this.customersButton.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            this.customersButton.Location = new Point(0, 180);
            this.customersButton.Name = "customersButton";
            this.customersButton.Size = new Size(200, 50);
            this.customersButton.TabIndex = 3;
            this.customersButton.Text = "👥 العملاء";
            this.customersButton.UseVisualStyleBackColor = true;
            this.customersButton.Click += new System.EventHandler(this.customersButton_Click);

            //
            // productsButton
            //
            this.productsButton.Dock = DockStyle.Top;
            this.productsButton.FlatStyle = FlatStyle.Flat;
            this.productsButton.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            this.productsButton.Location = new Point(0, 230);
            this.productsButton.Name = "productsButton";
            this.productsButton.Size = new Size(200, 50);
            this.productsButton.TabIndex = 4;
            this.productsButton.Text = "📦 المنتجات";
            this.productsButton.UseVisualStyleBackColor = true;
            this.productsButton.Click += new System.EventHandler(this.productsButton_Click);

            //
            // reportsButton
            //
            this.reportsButton.Dock = DockStyle.Top;
            this.reportsButton.FlatStyle = FlatStyle.Flat;
            this.reportsButton.Font = new Font("Segoe UI", 11F, FontStyle.Regular);
            this.reportsButton.Location = new Point(0, 280);
            this.reportsButton.Name = "reportsButton";
            this.reportsButton.Size = new Size(200, 50);
            this.reportsButton.TabIndex = 5;
            this.reportsButton.Text = "📊 التقارير";
            this.reportsButton.UseVisualStyleBackColor = true;
            this.reportsButton.Click += new System.EventHandler(this.reportsButton_Click);

            //
            // contentPanel
            //
            this.contentPanel.Controls.Add(this.statsPanel);
            this.contentPanel.Controls.Add(this.welcomeLabel);
            this.contentPanel.Dock = DockStyle.Fill;
            this.contentPanel.Location = new Point(0, 0);
            this.contentPanel.Name = "contentPanel";
            this.contentPanel.Padding = new Padding(20);
            this.contentPanel.Size = new Size(1000, 652);
            this.contentPanel.TabIndex = 1;

            //
            // welcomeLabel
            //
            this.welcomeLabel.Dock = DockStyle.Top;
            this.welcomeLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            this.welcomeLabel.Location = new Point(20, 20);
            this.welcomeLabel.Name = "welcomeLabel";
            this.welcomeLabel.Size = new Size(960, 60);
            this.welcomeLabel.TabIndex = 0;
            this.welcomeLabel.Text = "مرحباً بك في نظام إدارة المبيعات";
            this.welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // statsPanel
            //
            this.statsPanel.Controls.Add(this.monthSalesCard);
            this.statsPanel.Controls.Add(this.totalProductsCard);
            this.statsPanel.Controls.Add(this.totalCustomersCard);
            this.statsPanel.Controls.Add(this.todaySalesCard);
            this.statsPanel.Dock = DockStyle.Fill;
            this.statsPanel.Location = new Point(20, 80);
            this.statsPanel.Name = "statsPanel";
            this.statsPanel.Size = new Size(960, 552);
            this.statsPanel.TabIndex = 1;

            //
            // todaySalesCard
            //
            this.todaySalesCard.Controls.Add(this.todaySalesValue);
            this.todaySalesCard.Controls.Add(this.todaySalesLabel);
            this.todaySalesCard.Location = new Point(20, 20);
            this.todaySalesCard.Name = "todaySalesCard";
            this.todaySalesCard.Size = new Size(220, 120);
            this.todaySalesCard.TabIndex = 0;

            //
            // todaySalesLabel
            //
            this.todaySalesLabel.Dock = DockStyle.Top;
            this.todaySalesLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.todaySalesLabel.Location = new Point(0, 0);
            this.todaySalesLabel.Name = "todaySalesLabel";
            this.todaySalesLabel.Size = new Size(220, 40);
            this.todaySalesLabel.TabIndex = 0;
            this.todaySalesLabel.Text = "مبيعات اليوم";
            this.todaySalesLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // todaySalesValue
            //
            this.todaySalesValue.Dock = DockStyle.Fill;
            this.todaySalesValue.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.todaySalesValue.Location = new Point(0, 40);
            this.todaySalesValue.Name = "todaySalesValue";
            this.todaySalesValue.Size = new Size(220, 80);
            this.todaySalesValue.TabIndex = 1;
            this.todaySalesValue.Text = "0.00 ج.م";
            this.todaySalesValue.TextAlign = ContentAlignment.MiddleCenter;

            //
            // totalCustomersCard
            //
            this.totalCustomersCard.Controls.Add(this.totalCustomersValue);
            this.totalCustomersCard.Controls.Add(this.totalCustomersLabel);
            this.totalCustomersCard.Location = new Point(260, 20);
            this.totalCustomersCard.Name = "totalCustomersCard";
            this.totalCustomersCard.Size = new Size(220, 120);
            this.totalCustomersCard.TabIndex = 1;

            //
            // totalCustomersLabel
            //
            this.totalCustomersLabel.Dock = DockStyle.Top;
            this.totalCustomersLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.totalCustomersLabel.Location = new Point(0, 0);
            this.totalCustomersLabel.Name = "totalCustomersLabel";
            this.totalCustomersLabel.Size = new Size(220, 40);
            this.totalCustomersLabel.TabIndex = 0;
            this.totalCustomersLabel.Text = "إجمالي العملاء";
            this.totalCustomersLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // totalCustomersValue
            //
            this.totalCustomersValue.Dock = DockStyle.Fill;
            this.totalCustomersValue.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.totalCustomersValue.Location = new Point(0, 40);
            this.totalCustomersValue.Name = "totalCustomersValue";
            this.totalCustomersValue.Size = new Size(220, 80);
            this.totalCustomersValue.TabIndex = 1;
            this.totalCustomersValue.Text = "0";
            this.totalCustomersValue.TextAlign = ContentAlignment.MiddleCenter;

            //
            // totalProductsCard
            //
            this.totalProductsCard.Controls.Add(this.totalProductsValue);
            this.totalProductsCard.Controls.Add(this.totalProductsLabel);
            this.totalProductsCard.Location = new Point(500, 20);
            this.totalProductsCard.Name = "totalProductsCard";
            this.totalProductsCard.Size = new Size(220, 120);
            this.totalProductsCard.TabIndex = 2;

            //
            // totalProductsLabel
            //
            this.totalProductsLabel.Dock = DockStyle.Top;
            this.totalProductsLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.totalProductsLabel.Location = new Point(0, 0);
            this.totalProductsLabel.Name = "totalProductsLabel";
            this.totalProductsLabel.Size = new Size(220, 40);
            this.totalProductsLabel.TabIndex = 0;
            this.totalProductsLabel.Text = "إجمالي المنتجات";
            this.totalProductsLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // totalProductsValue
            //
            this.totalProductsValue.Dock = DockStyle.Fill;
            this.totalProductsValue.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.totalProductsValue.Location = new Point(0, 40);
            this.totalProductsValue.Name = "totalProductsValue";
            this.totalProductsValue.Size = new Size(220, 80);
            this.totalProductsValue.TabIndex = 1;
            this.totalProductsValue.Text = "0";
            this.totalProductsValue.TextAlign = ContentAlignment.MiddleCenter;

            //
            // monthSalesCard
            //
            this.monthSalesCard.Controls.Add(this.monthSalesValue);
            this.monthSalesCard.Controls.Add(this.monthSalesLabel);
            this.monthSalesCard.Location = new Point(740, 20);
            this.monthSalesCard.Name = "monthSalesCard";
            this.monthSalesCard.Size = new Size(220, 120);
            this.monthSalesCard.TabIndex = 3;

            //
            // monthSalesLabel
            //
            this.monthSalesLabel.Dock = DockStyle.Top;
            this.monthSalesLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.monthSalesLabel.Location = new Point(0, 0);
            this.monthSalesLabel.Name = "monthSalesLabel";
            this.monthSalesLabel.Size = new Size(220, 40);
            this.monthSalesLabel.TabIndex = 0;
            this.monthSalesLabel.Text = "مبيعات الشهر";
            this.monthSalesLabel.TextAlign = ContentAlignment.MiddleCenter;

            //
            // monthSalesValue
            //
            this.monthSalesValue.Dock = DockStyle.Fill;
            this.monthSalesValue.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.monthSalesValue.Location = new Point(0, 40);
            this.monthSalesValue.Name = "monthSalesValue";
            this.monthSalesValue.Size = new Size(220, 80);
            this.monthSalesValue.TabIndex = 1;
            this.monthSalesValue.Text = "0.00 ج.م";
            this.monthSalesValue.TextAlign = ContentAlignment.MiddleCenter;

            //
            // MainForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 700);
            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.menuStrip);
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "نظام إدارة المبيعات";
            this.WindowState = FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.MainForm_Load);

            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.mainPanel.ResumeLayout(false);
            this.sidePanel.ResumeLayout(false);
            this.logoPanel.ResumeLayout(false);
            this.contentPanel.ResumeLayout(false);
            this.statsPanel.ResumeLayout(false);
            this.todaySalesCard.ResumeLayout(false);
            this.totalCustomersCard.ResumeLayout(false);
            this.totalProductsCard.ResumeLayout(false);
            this.monthSalesCard.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip;
        private ToolStripMenuItem fileMenuItem;
        private ToolStripMenuItem exitMenuItem;
        private ToolStripMenuItem salesMenuItem;
        private ToolStripMenuItem newSaleMenuItem;
        private ToolStripMenuItem viewSalesMenuItem;
        private ToolStripMenuItem customersMenuItem;
        private ToolStripMenuItem manageCustomersMenuItem;
        private ToolStripMenuItem productsMenuItem;
        private ToolStripMenuItem manageProductsMenuItem;
        private ToolStripMenuItem reportsMenuItem;
        private ToolStripMenuItem salesReportMenuItem;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private Panel mainPanel;
        private Panel sidePanel;
        private Panel logoPanel;
        private Label logoLabel;
        private Button dashboardButton;
        private Button salesButton;
        private Button customersButton;
        private Button productsButton;
        private Button reportsButton;
        private Panel contentPanel;
        private Label welcomeLabel;
        private Panel statsPanel;
        private Panel todaySalesCard;
        private Label todaySalesLabel;
        private Label todaySalesValue;
        private Panel totalCustomersCard;
        private Label totalCustomersLabel;
        private Label totalCustomersValue;
        private Panel totalProductsCard;
        private Label totalProductsLabel;
        private Label totalProductsValue;
        private Panel monthSalesCard;
        private Label monthSalesLabel;
        private Label monthSalesValue;
    }
}
