using SalesManagementSystem.UI;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    partial class AddProductForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.headerPanel = new Panel();
            this.titleLabel = new Label();
            this.closeButton = new Button();
            this.contentPanel = new Panel();
            this.formCard = new Panel();
            this.namePanel = new Panel();
            this.nameLabel = new Label();
            this.nameTextBox = new TextBox();
            this.descriptionPanel = new Panel();
            this.descriptionLabel = new Label();
            this.descriptionTextBox = new TextBox();
            this.pricePanel = new Panel();
            this.priceLabel = new Label();
            this.priceTextBox = new TextBox();
            this.stockPanel = new Panel();
            this.stockLabel = new Label();
            this.stockTextBox = new TextBox();
            this.categoryPanel = new Panel();
            this.categoryLabel = new Label();
            this.categoryComboBox = new ComboBox();
            this.buttonPanel = new Panel();
            this.saveButton = new Button();
            this.cancelButton = new Button();
            this.clearButton = new Button();
            
            this.mainPanel.SuspendLayout();
            this.headerPanel.SuspendLayout();
            this.contentPanel.SuspendLayout();
            this.formCard.SuspendLayout();
            this.namePanel.SuspendLayout();
            this.descriptionPanel.SuspendLayout();
            this.pricePanel.SuspendLayout();
            this.stockPanel.SuspendLayout();
            this.categoryPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // mainPanel
            // 
            this.mainPanel.Controls.Add(this.contentPanel);
            this.mainPanel.Controls.Add(this.headerPanel);
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Location = new Point(0, 0);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Size = new Size(550, 650);
            this.mainPanel.TabIndex = 0;
            
            // 
            // headerPanel
            // 
            this.headerPanel.Controls.Add(this.closeButton);
            this.headerPanel.Controls.Add(this.titleLabel);
            this.headerPanel.Dock = DockStyle.Top;
            this.headerPanel.Location = new Point(0, 0);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Size = new Size(550, 60);
            this.headerPanel.TabIndex = 0;
            
            // 
            // titleLabel
            // 
            this.titleLabel.Dock = DockStyle.Fill;
            this.titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            this.titleLabel.Location = new Point(0, 0);
            this.titleLabel.Name = "titleLabel";
            this.titleLabel.Size = new Size(490, 60);
            this.titleLabel.TabIndex = 0;
            this.titleLabel.Text = "إضافة منتج جديد";
            this.titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // 
            // closeButton
            // 
            this.closeButton.Dock = DockStyle.Right;
            this.closeButton.FlatStyle = FlatStyle.Flat;
            this.closeButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.closeButton.Location = new Point(490, 0);
            this.closeButton.Name = "closeButton";
            this.closeButton.Size = new Size(60, 60);
            this.closeButton.TabIndex = 1;
            this.closeButton.Text = "✕";
            this.closeButton.UseVisualStyleBackColor = true;
            this.closeButton.Click += new System.EventHandler(this.closeButton_Click);
            
            // 
            // contentPanel
            // 
            this.contentPanel.Controls.Add(this.formCard);
            this.contentPanel.Dock = DockStyle.Fill;
            this.contentPanel.Location = new Point(0, 60);
            this.contentPanel.Name = "contentPanel";
            this.contentPanel.Padding = new Padding(30);
            this.contentPanel.Size = new Size(550, 590);
            this.contentPanel.TabIndex = 1;
            
            // 
            // formCard
            // 
            this.formCard.Controls.Add(this.buttonPanel);
            this.formCard.Controls.Add(this.categoryPanel);
            this.formCard.Controls.Add(this.stockPanel);
            this.formCard.Controls.Add(this.pricePanel);
            this.formCard.Controls.Add(this.descriptionPanel);
            this.formCard.Controls.Add(this.namePanel);
            this.formCard.Dock = DockStyle.Fill;
            this.formCard.Location = new Point(30, 30);
            this.formCard.Name = "formCard";
            this.formCard.Padding = new Padding(30);
            this.formCard.Size = new Size(490, 530);
            this.formCard.TabIndex = 0;
            
            // 
            // namePanel
            // 
            this.namePanel.Controls.Add(this.nameTextBox);
            this.namePanel.Controls.Add(this.nameLabel);
            this.namePanel.Dock = DockStyle.Top;
            this.namePanel.Location = new Point(30, 30);
            this.namePanel.Name = "namePanel";
            this.namePanel.Size = new Size(430, 80);
            this.namePanel.TabIndex = 0;
            
            // 
            // nameLabel
            // 
            this.nameLabel.Dock = DockStyle.Top;
            this.nameLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.nameLabel.Location = new Point(0, 0);
            this.nameLabel.Name = "nameLabel";
            this.nameLabel.Size = new Size(430, 30);
            this.nameLabel.TabIndex = 0;
            this.nameLabel.Text = "اسم المنتج *";
            this.nameLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // nameTextBox
            // 
            this.nameTextBox.Dock = DockStyle.Fill;
            this.nameTextBox.Font = new Font("Segoe UI", 12F);
            this.nameTextBox.Location = new Point(0, 30);
            this.nameTextBox.Name = "nameTextBox";
            this.nameTextBox.PlaceholderText = "أدخل اسم المنتج";
            this.nameTextBox.Size = new Size(430, 29);
            this.nameTextBox.TabIndex = 1;
            
            // 
            // descriptionPanel
            // 
            this.descriptionPanel.Controls.Add(this.descriptionTextBox);
            this.descriptionPanel.Controls.Add(this.descriptionLabel);
            this.descriptionPanel.Dock = DockStyle.Top;
            this.descriptionPanel.Location = new Point(30, 110);
            this.descriptionPanel.Name = "descriptionPanel";
            this.descriptionPanel.Size = new Size(430, 100);
            this.descriptionPanel.TabIndex = 1;
            
            // 
            // descriptionLabel
            // 
            this.descriptionLabel.Dock = DockStyle.Top;
            this.descriptionLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.descriptionLabel.Location = new Point(0, 0);
            this.descriptionLabel.Name = "descriptionLabel";
            this.descriptionLabel.Size = new Size(430, 30);
            this.descriptionLabel.TabIndex = 0;
            this.descriptionLabel.Text = "وصف المنتج";
            this.descriptionLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // descriptionTextBox
            // 
            this.descriptionTextBox.Dock = DockStyle.Fill;
            this.descriptionTextBox.Font = new Font("Segoe UI", 12F);
            this.descriptionTextBox.Location = new Point(0, 30);
            this.descriptionTextBox.Multiline = true;
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.PlaceholderText = "أدخل وصف المنتج";
            this.descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            this.descriptionTextBox.Size = new Size(430, 70);
            this.descriptionTextBox.TabIndex = 1;
            
            // 
            // pricePanel
            // 
            this.pricePanel.Controls.Add(this.priceTextBox);
            this.pricePanel.Controls.Add(this.priceLabel);
            this.pricePanel.Dock = DockStyle.Top;
            this.pricePanel.Location = new Point(30, 210);
            this.pricePanel.Name = "pricePanel";
            this.pricePanel.Size = new Size(430, 80);
            this.pricePanel.TabIndex = 2;
            
            // 
            // priceLabel
            // 
            this.priceLabel.Dock = DockStyle.Top;
            this.priceLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.priceLabel.Location = new Point(0, 0);
            this.priceLabel.Name = "priceLabel";
            this.priceLabel.Size = new Size(430, 30);
            this.priceLabel.TabIndex = 0;
            this.priceLabel.Text = "السعر (ج.م) *";
            this.priceLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // priceTextBox
            // 
            this.priceTextBox.Dock = DockStyle.Fill;
            this.priceTextBox.Font = new Font("Segoe UI", 12F);
            this.priceTextBox.Location = new Point(0, 30);
            this.priceTextBox.Name = "priceTextBox";
            this.priceTextBox.PlaceholderText = "أدخل سعر المنتج";
            this.priceTextBox.Size = new Size(430, 29);
            this.priceTextBox.TabIndex = 1;
            this.priceTextBox.KeyPress += new KeyPressEventHandler(this.priceTextBox_KeyPress);
            
            // 
            // stockPanel
            // 
            this.stockPanel.Controls.Add(this.stockTextBox);
            this.stockPanel.Controls.Add(this.stockLabel);
            this.stockPanel.Dock = DockStyle.Top;
            this.stockPanel.Location = new Point(30, 290);
            this.stockPanel.Name = "stockPanel";
            this.stockPanel.Size = new Size(430, 80);
            this.stockPanel.TabIndex = 3;
            
            // 
            // stockLabel
            // 
            this.stockLabel.Dock = DockStyle.Top;
            this.stockLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.stockLabel.Location = new Point(0, 0);
            this.stockLabel.Name = "stockLabel";
            this.stockLabel.Size = new Size(430, 30);
            this.stockLabel.TabIndex = 0;
            this.stockLabel.Text = "الكمية في المخزون *";
            this.stockLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // stockTextBox
            // 
            this.stockTextBox.Dock = DockStyle.Fill;
            this.stockTextBox.Font = new Font("Segoe UI", 12F);
            this.stockTextBox.Location = new Point(0, 30);
            this.stockTextBox.Name = "stockTextBox";
            this.stockTextBox.PlaceholderText = "أدخل الكمية المتاحة";
            this.stockTextBox.Size = new Size(430, 29);
            this.stockTextBox.TabIndex = 1;
            this.stockTextBox.KeyPress += new KeyPressEventHandler(this.stockTextBox_KeyPress);
            
            // 
            // categoryPanel
            // 
            this.categoryPanel.Controls.Add(this.categoryComboBox);
            this.categoryPanel.Controls.Add(this.categoryLabel);
            this.categoryPanel.Dock = DockStyle.Top;
            this.categoryPanel.Location = new Point(30, 370);
            this.categoryPanel.Name = "categoryPanel";
            this.categoryPanel.Size = new Size(430, 80);
            this.categoryPanel.TabIndex = 4;
            
            // 
            // categoryLabel
            // 
            this.categoryLabel.Dock = DockStyle.Top;
            this.categoryLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.categoryLabel.Location = new Point(0, 0);
            this.categoryLabel.Name = "categoryLabel";
            this.categoryLabel.Size = new Size(430, 30);
            this.categoryLabel.TabIndex = 0;
            this.categoryLabel.Text = "فئة المنتج";
            this.categoryLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // categoryComboBox
            // 
            this.categoryComboBox.Dock = DockStyle.Fill;
            this.categoryComboBox.DropDownStyle = ComboBoxStyle.DropDown;
            this.categoryComboBox.Font = new Font("Segoe UI", 12F);
            this.categoryComboBox.Location = new Point(0, 30);
            this.categoryComboBox.Name = "categoryComboBox";
            this.categoryComboBox.Size = new Size(430, 29);
            this.categoryComboBox.TabIndex = 1;
            
            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.clearButton);
            this.buttonPanel.Controls.Add(this.cancelButton);
            this.buttonPanel.Controls.Add(this.saveButton);
            this.buttonPanel.Dock = DockStyle.Bottom;
            this.buttonPanel.Location = new Point(30, 470);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(430, 60);
            this.buttonPanel.TabIndex = 5;
            
            // 
            // saveButton
            // 
            this.saveButton.Dock = DockStyle.Right;
            this.saveButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.saveButton.Location = new Point(310, 0);
            this.saveButton.Name = "saveButton";
            this.saveButton.Size = new Size(120, 60);
            this.saveButton.TabIndex = 0;
            this.saveButton.Text = "💾 حفظ";
            this.saveButton.UseVisualStyleBackColor = true;
            this.saveButton.Click += new System.EventHandler(this.saveButton_Click);
            
            // 
            // cancelButton
            // 
            this.cancelButton.Dock = DockStyle.Right;
            this.cancelButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.cancelButton.Location = new Point(190, 0);
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.Size = new Size(120, 60);
            this.cancelButton.TabIndex = 1;
            this.cancelButton.Text = "❌ إلغاء";
            this.cancelButton.UseVisualStyleBackColor = true;
            this.cancelButton.Click += new System.EventHandler(this.cancelButton_Click);
            
            // 
            // clearButton
            // 
            this.clearButton.Dock = DockStyle.Right;
            this.clearButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.clearButton.Location = new Point(70, 0);
            this.clearButton.Name = "clearButton";
            this.clearButton.Size = new Size(120, 60);
            this.clearButton.TabIndex = 2;
            this.clearButton.Text = "🗑️ مسح";
            this.clearButton.UseVisualStyleBackColor = true;
            this.clearButton.Click += new System.EventHandler(this.clearButton_Click);
            
            // 
            // AddProductForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(550, 650);
            this.Controls.Add(this.mainPanel);
            this.FormBorderStyle = FormBorderStyle.None;
            this.Name = "AddProductForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة منتج جديد";
            this.Load += new System.EventHandler(this.AddProductForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            this.headerPanel.ResumeLayout(false);
            this.contentPanel.ResumeLayout(false);
            this.formCard.ResumeLayout(false);
            this.namePanel.ResumeLayout(false);
            this.namePanel.PerformLayout();
            this.descriptionPanel.ResumeLayout(false);
            this.descriptionPanel.PerformLayout();
            this.pricePanel.ResumeLayout(false);
            this.pricePanel.PerformLayout();
            this.stockPanel.ResumeLayout(false);
            this.stockPanel.PerformLayout();
            this.categoryPanel.ResumeLayout(false);
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private Panel mainPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Button closeButton;
        private Panel contentPanel;
        private Panel formCard;
        private Panel namePanel;
        private Label nameLabel;
        private TextBox nameTextBox;
        private Panel descriptionPanel;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Panel pricePanel;
        private Label priceLabel;
        private TextBox priceTextBox;
        private Panel stockPanel;
        private Label stockLabel;
        private TextBox stockTextBox;
        private Panel categoryPanel;
        private Label categoryLabel;
        private ComboBox categoryComboBox;
        private Panel buttonPanel;
        private Button saveButton;
        private Button cancelButton;
        private Button clearButton;
    }
}
