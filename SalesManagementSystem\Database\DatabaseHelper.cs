using System;
using System.Data.SQLite;
using System.IO;

namespace SalesManagementSystem.Database
{
    public class DatabaseHelper
    {
        private static string connectionString = "Data Source=SalesDB.sqlite;Version=3;";
        
        public static void InitializeDatabase()
        {
            if (!File.Exists("SalesDB.sqlite"))
            {
                SQLiteConnection.CreateFile("SalesDB.sqlite");
            }
            
            CreateTables();
        }
        
        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(connectionString);
        }
        
        private static void CreateTables()
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                
                // جدول العملاء
                string createCustomersTable = @"
                    CREATE TABLE IF NOT EXISTS Customers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Phone TEXT,
                        Email TEXT,
                        Address TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                    )";
                
                // جدول المنتجات
                string createProductsTable = @"
                    CREATE TABLE IF NOT EXISTS Products (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        Price DECIMAL(10,2) NOT NULL,
                        Stock INTEGER DEFAULT 0,
                        Category TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                    )";
                
                // جدول المبيعات
                string createSalesTable = @"
                    CREATE TABLE IF NOT EXISTS Sales (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CustomerId INTEGER,
                        SaleDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        TotalAmount DECIMAL(10,2) NOT NULL,
                        Discount DECIMAL(10,2) DEFAULT 0,
                        FinalAmount DECIMAL(10,2) NOT NULL,
                        Notes TEXT,
                        FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                    )";
                
                // جدول تفاصيل المبيعات
                string createSaleItemsTable = @"
                    CREATE TABLE IF NOT EXISTS SaleItems (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        SaleId INTEGER,
                        ProductId INTEGER,
                        Quantity INTEGER NOT NULL,
                        UnitPrice DECIMAL(10,2) NOT NULL,
                        TotalPrice DECIMAL(10,2) NOT NULL,
                        FOREIGN KEY (SaleId) REFERENCES Sales(Id),
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )";
                
                using (var command = new SQLiteCommand(createCustomersTable, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                using (var command = new SQLiteCommand(createProductsTable, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                using (var command = new SQLiteCommand(createSalesTable, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                using (var command = new SQLiteCommand(createSaleItemsTable, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                // إدراج بيانات تجريبية
                InsertSampleData(connection);
            }
        }
        
        private static void InsertSampleData(SQLiteConnection connection)
        {
            // التحقق من وجود بيانات
            string checkData = "SELECT COUNT(*) FROM Customers";
            using (var command = new SQLiteCommand(checkData, connection))
            {
                int count = Convert.ToInt32(command.ExecuteScalar());
                if (count > 0) return; // البيانات موجودة بالفعل
            }
            
            // إدراج عملاء تجريبيين
            string insertCustomers = @"
                INSERT INTO Customers (Name, Phone, Email, Address) VALUES
                ('أحمد محمد', '01234567890', '<EMAIL>', 'القاهرة، مصر'),
                ('فاطمة علي', '01987654321', '<EMAIL>', 'الإسكندرية، مصر'),
                ('محمد حسن', '01122334455', '<EMAIL>', 'الجيزة، مصر')";
            
            using (var command = new SQLiteCommand(insertCustomers, connection))
            {
                command.ExecuteNonQuery();
            }
            
            // إدراج منتجات تجريبية
            string insertProducts = @"
                INSERT INTO Products (Name, Description, Price, Stock, Category) VALUES
                ('لابتوب Dell', 'لابتوب Dell Inspiron 15', 15000.00, 10, 'إلكترونيات'),
                ('ماوس لاسلكي', 'ماوس لاسلكي عالي الجودة', 250.00, 50, 'إكسسوارات'),
                ('لوحة مفاتيح', 'لوحة مفاتيح ميكانيكية', 800.00, 25, 'إكسسوارات'),
                ('شاشة 24 بوصة', 'شاشة LED 24 بوصة', 3500.00, 15, 'إلكترونيات'),
                ('سماعات', 'سماعات استريو عالية الجودة', 450.00, 30, 'إكسسوارات')";
            
            using (var command = new SQLiteCommand(insertProducts, connection))
            {
                command.ExecuteNonQuery();
            }
        }
    }
}
