using System;
using System.Drawing;
using System.Windows.Forms;
using SalesManagementSystem.Database;
using SalesManagementSystem.DAL;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class MainForm : Form
    {
        private CustomerDAL customerDAL;
        private ProductDAL productDAL;
        private SaleDAL saleDAL;
        
        public MainForm()
        {
            InitializeComponent();
            customerDAL = new CustomerDAL();
            productDAL = new ProductDAL();
            saleDAL = new SaleDAL();
        }
        
        private void MainForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            DarkTheme.ApplyToForm(this);
            
            // تخصيص الألوان للبطاقات
            SetupCards();
            
            // تحديث الإحصائيات
            UpdateStatistics();
            
            // تهيئة قاعدة البيانات
            DatabaseHelper.InitializeDatabase();
        }
        
        private void SetupCards()
        {
            // بطاقة مبيعات اليوم
            todaySalesCard.BackColor = DarkTheme.CardColor;
            todaySalesCard.BorderStyle = BorderStyle.FixedSingle;
            todaySalesLabel.ForeColor = DarkTheme.SecondaryTextColor;
            todaySalesValue.ForeColor = DarkTheme.SuccessColor;
            
            // بطاقة إجمالي العملاء
            totalCustomersCard.BackColor = DarkTheme.CardColor;
            totalCustomersCard.BorderStyle = BorderStyle.FixedSingle;
            totalCustomersLabel.ForeColor = DarkTheme.SecondaryTextColor;
            totalCustomersValue.ForeColor = DarkTheme.PrimaryColor;
            
            // بطاقة إجمالي المنتجات
            totalProductsCard.BackColor = DarkTheme.CardColor;
            totalProductsCard.BorderStyle = BorderStyle.FixedSingle;
            totalProductsLabel.ForeColor = DarkTheme.SecondaryTextColor;
            totalProductsValue.ForeColor = DarkTheme.AccentColor;
            
            // بطاقة مبيعات الشهر
            monthSalesCard.BackColor = DarkTheme.CardColor;
            monthSalesCard.BorderStyle = BorderStyle.FixedSingle;
            monthSalesLabel.ForeColor = DarkTheme.SecondaryTextColor;
            monthSalesValue.ForeColor = DarkTheme.SuccessColor;
            
            // تخصيص الأزرار الجانبية
            SetupSideButtons();
        }
        
        private void SetupSideButtons()
        {
            Button[] buttons = { dashboardButton, salesButton, customersButton, productsButton, reportsButton };
            
            foreach (Button button in buttons)
            {
                button.BackColor = DarkTheme.SurfaceColor;
                button.ForeColor = DarkTheme.TextColor;
                button.FlatStyle = FlatStyle.Flat;
                button.FlatAppearance.BorderSize = 0;
                button.FlatAppearance.MouseOverBackColor = DarkTheme.HoverColor;
                button.FlatAppearance.MouseDownBackColor = DarkTheme.PrimaryColor;
                button.TextAlign = ContentAlignment.MiddleLeft;
                button.Padding = new Padding(20, 0, 0, 0);
            }
            
            // تمييز زر لوحة التحكم كزر نشط
            dashboardButton.BackColor = DarkTheme.PrimaryColor;
        }
        
        private void UpdateStatistics()
        {
            try
            {
                // مبيعات اليوم
                var todaySales = saleDAL.GetTotalSalesAmount(DateTime.Today, DateTime.Today);
                todaySalesValue.Text = $"{todaySales:N2} ج.م";
                
                // إجمالي العملاء
                var customers = customerDAL.GetAllCustomers();
                totalCustomersValue.Text = customers.Count.ToString();
                
                // إجمالي المنتجات
                var products = productDAL.GetAllProducts();
                totalProductsValue.Text = products.Count.ToString();
                
                // مبيعات الشهر
                var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                var monthSales = saleDAL.GetTotalSalesAmount(startOfMonth, endOfMonth);
                monthSalesValue.Text = $"{monthSales:N2} ج.م";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ResetButtonColors()
        {
            Button[] buttons = { dashboardButton, salesButton, customersButton, productsButton, reportsButton };
            
            foreach (Button button in buttons)
            {
                button.BackColor = DarkTheme.SurfaceColor;
            }
        }
        
        private void SetActiveButton(Button activeButton)
        {
            ResetButtonColors();
            activeButton.BackColor = DarkTheme.PrimaryColor;
        }
        
        // أحداث القائمة
        private void exitMenuItem_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
        
        private void newSaleMenuItem_Click(object sender, EventArgs e)
        {
            OpenSalesForm();
        }
        
        private void viewSalesMenuItem_Click(object sender, EventArgs e)
        {
            OpenSalesListForm();
        }
        
        private void manageCustomersMenuItem_Click(object sender, EventArgs e)
        {
            OpenCustomersForm();
        }
        
        private void manageProductsMenuItem_Click(object sender, EventArgs e)
        {
            OpenProductsForm();
        }
        
        private void salesReportMenuItem_Click(object sender, EventArgs e)
        {
            OpenReportsForm();
        }
        
        // أحداث الأزرار الجانبية
        private void dashboardButton_Click(object sender, EventArgs e)
        {
            SetActiveButton(dashboardButton);
            ShowDashboard();
        }
        
        private void salesButton_Click(object sender, EventArgs e)
        {
            SetActiveButton(salesButton);
            OpenSalesForm();
        }
        
        private void customersButton_Click(object sender, EventArgs e)
        {
            SetActiveButton(customersButton);
            OpenCustomersForm();
        }
        
        private void productsButton_Click(object sender, EventArgs e)
        {
            SetActiveButton(productsButton);
            OpenProductsForm();
        }
        
        private void reportsButton_Click(object sender, EventArgs e)
        {
            SetActiveButton(reportsButton);
            OpenReportsForm();
        }
        
        // دوال فتح النماذج
        private void ShowDashboard()
        {
            // إظهار لوحة التحكم (الصفحة الحالية)
            UpdateStatistics();
        }
        
        private void OpenSalesForm()
        {
            // سيتم إنشاؤها لاحقاً
            MessageBox.Show("سيتم إنشاء نموذج المبيعات قريباً", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void OpenSalesListForm()
        {
            // سيتم إنشاؤها لاحقاً
            MessageBox.Show("سيتم إنشاء نموذج قائمة المبيعات قريباً", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void OpenCustomersForm()
        {
            var customersForm = new CustomersForm();
            customersForm.ShowDialog();
            UpdateStatistics(); // تحديث الإحصائيات بعد إغلاق النموذج
        }
        
        private void OpenProductsForm()
        {
            var productsForm = new ProductsForm();
            productsForm.ShowDialog();
            UpdateStatistics(); // تحديث الإحصائيات بعد إغلاق النموذج
        }
        
        private void OpenReportsForm()
        {
            var reportsForm = new ReportsForm();
            reportsForm.ShowDialog();
        }
    }
}
