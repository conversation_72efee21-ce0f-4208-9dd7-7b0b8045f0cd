using SalesManagementSystem.UI;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Reporting.WinForms;

namespace SalesManagementSystem.Forms
{
    partial class ReportsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.reportViewer = new ReportViewer();
            this.controlPanel = new Panel();
            this.titleLabel = new Label();
            this.filterPanel = new Panel();
            this.startDateLabel = new Label();
            this.startDatePicker = new DateTimePicker();
            this.endDateLabel = new Label();
            this.endDatePicker = new DateTimePicker();
            this.generateButton = new Button();
            this.exportButton = new Button();
            this.printButton = new Button();
            
            this.mainPanel.SuspendLayout();
            this.controlPanel.SuspendLayout();
            this.filterPanel.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // mainPanel
            // 
            this.mainPanel.Controls.Add(this.reportViewer);
            this.mainPanel.Controls.Add(this.controlPanel);
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Location = new Point(0, 0);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Size = new Size(1000, 700);
            this.mainPanel.TabIndex = 0;
            
            // 
            // controlPanel
            // 
            this.controlPanel.Controls.Add(this.filterPanel);
            this.controlPanel.Controls.Add(this.titleLabel);
            this.controlPanel.Dock = DockStyle.Top;
            this.controlPanel.Location = new Point(0, 0);
            this.controlPanel.Name = "controlPanel";
            this.controlPanel.Size = new Size(1000, 100);
            this.controlPanel.TabIndex = 0;
            
            // 
            // titleLabel
            // 
            this.titleLabel.Dock = DockStyle.Top;
            this.titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.titleLabel.Location = new Point(0, 0);
            this.titleLabel.Name = "titleLabel";
            this.titleLabel.Size = new Size(1000, 40);
            this.titleLabel.TabIndex = 0;
            this.titleLabel.Text = "تقارير المبيعات";
            this.titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // 
            // filterPanel
            // 
            this.filterPanel.Controls.Add(this.printButton);
            this.filterPanel.Controls.Add(this.exportButton);
            this.filterPanel.Controls.Add(this.generateButton);
            this.filterPanel.Controls.Add(this.endDatePicker);
            this.filterPanel.Controls.Add(this.endDateLabel);
            this.filterPanel.Controls.Add(this.startDatePicker);
            this.filterPanel.Controls.Add(this.startDateLabel);
            this.filterPanel.Dock = DockStyle.Fill;
            this.filterPanel.Location = new Point(0, 40);
            this.filterPanel.Name = "filterPanel";
            this.filterPanel.Padding = new Padding(20, 10, 20, 10);
            this.filterPanel.Size = new Size(1000, 60);
            this.filterPanel.TabIndex = 1;
            
            // 
            // startDateLabel
            // 
            this.startDateLabel.AutoSize = true;
            this.startDateLabel.Location = new Point(20, 20);
            this.startDateLabel.Name = "startDateLabel";
            this.startDateLabel.Size = new Size(50, 15);
            this.startDateLabel.TabIndex = 0;
            this.startDateLabel.Text = "من تاريخ:";
            
            // 
            // startDatePicker
            // 
            this.startDatePicker.Format = DateTimePickerFormat.Short;
            this.startDatePicker.Location = new Point(80, 17);
            this.startDatePicker.Name = "startDatePicker";
            this.startDatePicker.Size = new Size(120, 23);
            this.startDatePicker.TabIndex = 1;
            
            // 
            // endDateLabel
            // 
            this.endDateLabel.AutoSize = true;
            this.endDateLabel.Location = new Point(220, 20);
            this.endDateLabel.Name = "endDateLabel";
            this.endDateLabel.Size = new Size(55, 15);
            this.endDateLabel.TabIndex = 2;
            this.endDateLabel.Text = "إلى تاريخ:";
            
            // 
            // endDatePicker
            // 
            this.endDatePicker.Format = DateTimePickerFormat.Short;
            this.endDatePicker.Location = new Point(280, 17);
            this.endDatePicker.Name = "endDatePicker";
            this.endDatePicker.Size = new Size(120, 23);
            this.endDatePicker.TabIndex = 3;
            
            // 
            // generateButton
            // 
            this.generateButton.Location = new Point(420, 15);
            this.generateButton.Name = "generateButton";
            this.generateButton.Size = new Size(100, 30);
            this.generateButton.TabIndex = 4;
            this.generateButton.Text = "إنشاء التقرير";
            this.generateButton.UseVisualStyleBackColor = true;
            this.generateButton.Click += new System.EventHandler(this.generateButton_Click);
            
            // 
            // exportButton
            // 
            this.exportButton.Location = new Point(530, 15);
            this.exportButton.Name = "exportButton";
            this.exportButton.Size = new Size(100, 30);
            this.exportButton.TabIndex = 5;
            this.exportButton.Text = "تصدير PDF";
            this.exportButton.UseVisualStyleBackColor = true;
            this.exportButton.Click += new System.EventHandler(this.exportButton_Click);
            
            // 
            // printButton
            // 
            this.printButton.Location = new Point(640, 15);
            this.printButton.Name = "printButton";
            this.printButton.Size = new Size(100, 30);
            this.printButton.TabIndex = 6;
            this.printButton.Text = "طباعة";
            this.printButton.UseVisualStyleBackColor = true;
            this.printButton.Click += new System.EventHandler(this.printButton_Click);
            
            // 
            // reportViewer
            // 
            this.reportViewer.Dock = DockStyle.Fill;
            this.reportViewer.Location = new Point(0, 100);
            this.reportViewer.Name = "reportViewer";
            this.reportViewer.ServerReport.BearerToken = null;
            this.reportViewer.Size = new Size(1000, 600);
            this.reportViewer.TabIndex = 1;
            
            // 
            // ReportsForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Controls.Add(this.mainPanel);
            this.Name = "ReportsForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "تقارير المبيعات";
            this.WindowState = FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.ReportsForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            this.controlPanel.ResumeLayout(false);
            this.filterPanel.ResumeLayout(false);
            this.filterPanel.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Panel mainPanel;
        private Panel controlPanel;
        private Label titleLabel;
        private Panel filterPanel;
        private Label startDateLabel;
        private DateTimePicker startDatePicker;
        private Label endDateLabel;
        private DateTimePicker endDatePicker;
        private Button generateButton;
        private Button exportButton;
        private Button printButton;
        private ReportViewer reportViewer;
    }
}
