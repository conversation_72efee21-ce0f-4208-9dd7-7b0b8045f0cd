using System;
using System.Collections.Generic;
using System.Data.SQLite;
using SalesManagementSystem.Database;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.DAL
{
    public class SaleDAL
    {
        private CustomerDAL customerDAL = new CustomerDAL();
        private ProductDAL productDAL = new ProductDAL();
        
        public List<Sale> GetAllSales()
        {
            List<Sale> sales = new List<Sale>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Sales ORDER BY SaleDate DESC";
                
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var sale = new Sale
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            CustomerId = Convert.ToInt32(reader["CustomerId"]),
                            SaleDate = Convert.ToDateTime(reader["SaleDate"]),
                            TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                            Discount = Convert.ToDecimal(reader["Discount"]),
                            FinalAmount = Convert.ToDecimal(reader["FinalAmount"]),
                            Notes = reader["Notes"].ToString()
                        };
                        
                        sale.Customer = customerDAL.GetCustomerById(sale.CustomerId);
                        sale.SaleItems = GetSaleItems(sale.Id);
                        
                        sales.Add(sale);
                    }
                }
            }
            
            return sales;
        }
        
        public Sale GetSaleById(int id)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Sales WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var sale = new Sale
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                CustomerId = Convert.ToInt32(reader["CustomerId"]),
                                SaleDate = Convert.ToDateTime(reader["SaleDate"]),
                                TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                Discount = Convert.ToDecimal(reader["Discount"]),
                                FinalAmount = Convert.ToDecimal(reader["FinalAmount"]),
                                Notes = reader["Notes"].ToString()
                            };
                            
                            sale.Customer = customerDAL.GetCustomerById(sale.CustomerId);
                            sale.SaleItems = GetSaleItems(sale.Id);
                            
                            return sale;
                        }
                    }
                }
            }
            
            return null;
        }
        
        public int AddSale(Sale sale)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // إدراج المبيعة
                        string saleQuery = @"INSERT INTO Sales (CustomerId, SaleDate, TotalAmount, Discount, FinalAmount, Notes) 
                                           VALUES (@CustomerId, @SaleDate, @TotalAmount, @Discount, @FinalAmount, @Notes);
                                           SELECT last_insert_rowid();";
                        
                        int saleId;
                        using (var command = new SQLiteCommand(saleQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@CustomerId", sale.CustomerId);
                            command.Parameters.AddWithValue("@SaleDate", sale.SaleDate);
                            command.Parameters.AddWithValue("@TotalAmount", sale.TotalAmount);
                            command.Parameters.AddWithValue("@Discount", sale.Discount);
                            command.Parameters.AddWithValue("@FinalAmount", sale.FinalAmount);
                            command.Parameters.AddWithValue("@Notes", sale.Notes ?? "");
                            
                            saleId = Convert.ToInt32(command.ExecuteScalar());
                        }
                        
                        // إدراج عناصر المبيعة
                        foreach (var item in sale.SaleItems)
                        {
                            string itemQuery = @"INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, TotalPrice) 
                                               VALUES (@SaleId, @ProductId, @Quantity, @UnitPrice, @TotalPrice)";
                            
                            using (var command = new SQLiteCommand(itemQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@SaleId", saleId);
                                command.Parameters.AddWithValue("@ProductId", item.ProductId);
                                command.Parameters.AddWithValue("@Quantity", item.Quantity);
                                command.Parameters.AddWithValue("@UnitPrice", item.UnitPrice);
                                command.Parameters.AddWithValue("@TotalPrice", item.TotalPrice);
                                
                                command.ExecuteNonQuery();
                            }
                            
                            // تحديث المخزون
                            var product = productDAL.GetProductById(item.ProductId);
                            if (product != null)
                            {
                                productDAL.UpdateStock(item.ProductId, product.Stock - item.Quantity);
                            }
                        }
                        
                        transaction.Commit();
                        return saleId;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        
        public List<SaleItem> GetSaleItems(int saleId)
        {
            List<SaleItem> items = new List<SaleItem>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM SaleItems WHERE SaleId = @SaleId";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SaleId", saleId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var item = new SaleItem
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                SaleId = Convert.ToInt32(reader["SaleId"]),
                                ProductId = Convert.ToInt32(reader["ProductId"]),
                                Quantity = Convert.ToInt32(reader["Quantity"]),
                                UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                                TotalPrice = Convert.ToDecimal(reader["TotalPrice"])
                            };
                            
                            item.Product = productDAL.GetProductById(item.ProductId);
                            items.Add(item);
                        }
                    }
                }
            }
            
            return items;
        }
        
        public List<Sale> GetSalesByDateRange(DateTime startDate, DateTime endDate)
        {
            List<Sale> sales = new List<Sale>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Sales WHERE DATE(SaleDate) BETWEEN @StartDate AND @EndDate ORDER BY SaleDate DESC";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var sale = new Sale
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                CustomerId = Convert.ToInt32(reader["CustomerId"]),
                                SaleDate = Convert.ToDateTime(reader["SaleDate"]),
                                TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                Discount = Convert.ToDecimal(reader["Discount"]),
                                FinalAmount = Convert.ToDecimal(reader["FinalAmount"]),
                                Notes = reader["Notes"].ToString()
                            };
                            
                            sale.Customer = customerDAL.GetCustomerById(sale.CustomerId);
                            sales.Add(sale);
                        }
                    }
                }
            }
            
            return sales;
        }
        
        public decimal GetTotalSalesAmount(DateTime? startDate = null, DateTime? endDate = null)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT COALESCE(SUM(FinalAmount), 0) FROM Sales";
                
                if (startDate.HasValue && endDate.HasValue)
                {
                    query += " WHERE DATE(SaleDate) BETWEEN @StartDate AND @EndDate";
                }
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate.Value.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.Value.ToString("yyyy-MM-dd"));
                    }
                    
                    return Convert.ToDecimal(command.ExecuteScalar());
                }
            }
        }
    }
}
