🎯 نظام إدارة المبيعات - تعليمات التشغيل
===============================================

📋 المتطلبات:
-----------
✅ Windows 10 أو أحدث
✅ .NET 9.0 Runtime (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)
✅ 50 MB مساحة فارغة على القرص الصلب

🚀 كيفية التشغيل:
---------------

الطريقة الأولى - من خلال Visual Studio:
1. افتح ملف SalesManagementSystem.sln في Visual Studio
2. اضغط F5 أو انقر على "Start" لتشغيل البرنامج

الطريقة الثانية - من خلال سطر الأوامر:
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\Desktop\C#\ERP\SalesManagementSystem"
3. شغل الأمر:
   dotnet run

الطريقة الثالثة - تشغيل الملف المبني:
1. انتقل إلى مجلد:
   SalesManagementSystem\bin\Debug\net9.0-windows\
2. انقر مرتين على ملف SalesManagementSystem.exe

📱 كيفية الاستخدام:
-----------------

🏠 الشاشة الرئيسية:
- تعرض إحصائيات سريعة (مبيعات اليوم، إجمالي العملاء، إلخ)
- استخدم الأزرار الجانبية للتنقل بين الأقسام

👥 إدارة العملاء:
- انقر على "العملاء" من القائمة الجانبية
- "إضافة": لإضافة عميل جديد
- "تعديل": لتعديل عميل محدد
- "حذف": لحذف عميل محدد
- استخدم مربع البحث للعثور على عميل معين

📦 إدارة المنتجات:
- انقر على "المنتجات" من القائمة الجانبية
- "إضافة": لإضافة منتج جديد
- "تعديل": لتعديل منتج محدد
- "حذف": لحذف منتج محدد
- يمكن تحديد السعر والمخزون والفئة لكل منتج

📊 التقارير:
- انقر على "التقارير" من القائمة الجانبية
- حدد نطاق التاريخ المطلوب
- انقر "إنشاء التقرير" لعرض البيانات
- "تصدير PDF": لحفظ التقرير كملف PDF
- "طباعة": لطباعة التقرير مباشرة

🎨 المميزات الخاصة:
-----------------

🌙 النمط الداكن:
- تصميم عصري مريح للعين
- ألوان متناسقة ومتباينة
- واجهة احترافية

🔍 البحث السريع:
- ابحث في العملاء بالاسم أو الهاتف أو البريد الإلكتروني
- ابحث في المنتجات بالاسم أو الوصف أو الفئة
- نتائج فورية أثناء الكتابة

📈 الإحصائيات المباشرة:
- مبيعات اليوم الحالي
- إجمالي العملاء المسجلين
- إجمالي المنتجات المتاحة
- مبيعات الشهر الحالي

💾 البيانات التجريبية:
--------------------
عند التشغيل الأول، سيتم إنشاء:
✅ 3 عملاء نموذجيين
✅ 5 منتجات في فئات مختلفة
✅ قاعدة بيانات SQLite محلية (SalesDB.sqlite)

🔧 حل المشاكل الشائعة:
---------------------

❌ المشكلة: "لا يمكن العثور على .NET Runtime"
✅ الحل: قم بتحميل وتثبيت .NET 9.0 من موقع Microsoft

❌ المشكلة: "خطأ في قاعدة البيانات"
✅ الحل: احذف ملف SalesDB.sqlite وأعد تشغيل البرنامج

❌ المشكلة: "التقارير لا تعمل"
✅ الحل: تأكد من وجود مجلد Reports وملف SalesReport.rdlc

❌ المشكلة: "الخطوط غير واضحة"
✅ الحل: تأكد من إعدادات DPI في Windows (100% أو 125%)

📁 ملفات المشروع المهمة:
------------------------
- SalesDB.sqlite: قاعدة البيانات الرئيسية
- Reports/SalesReport.rdlc: قالب تقرير المبيعات
- SalesManagementSystem.exe: الملف التنفيذي الرئيسي

🔒 الأمان والنسخ الاحتياطي:
---------------------------
- يُنصح بعمل نسخة احتياطية من ملف SalesDB.sqlite بانتظام
- البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات عبر الإنترنت

📞 الدعم والمساعدة:
------------------
- راجع ملف README.md للمزيد من التفاصيل
- تحقق من التحديثات الجديدة بانتظام
- أبلغ عن أي مشاكل أو اقتراحات

🎉 نصائح للاستخدام الأمثل:
--------------------------
1. ابدأ بإدخال العملاء والمنتجات قبل تسجيل المبيعات
2. استخدم أسماء واضحة ومفهومة للمنتجات والفئات
3. راجع التقارير بانتظام لمتابعة الأداء
4. احتفظ بنسخة احتياطية من البيانات أسبوعياً

===============================================
تم التطوير بـ ❤️ للمجتمع العربي
نتمنى لك تجربة ممتعة ومفيدة! 🚀
