using SalesManagementSystem.UI;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    partial class AddCustomerForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.headerPanel = new Panel();
            this.titleLabel = new Label();
            this.closeButton = new Button();
            this.contentPanel = new Panel();
            this.formCard = new Panel();
            this.namePanel = new Panel();
            this.nameLabel = new Label();
            this.nameTextBox = new TextBox();
            this.phonePanel = new Panel();
            this.phoneLabel = new Label();
            this.phoneTextBox = new TextBox();
            this.emailPanel = new Panel();
            this.emailLabel = new Label();
            this.emailTextBox = new TextBox();
            this.addressPanel = new Panel();
            this.addressLabel = new Label();
            this.addressTextBox = new TextBox();
            this.buttonPanel = new Panel();
            this.saveButton = new Button();
            this.cancelButton = new Button();
            this.clearButton = new Button();
            
            this.mainPanel.SuspendLayout();
            this.headerPanel.SuspendLayout();
            this.contentPanel.SuspendLayout();
            this.formCard.SuspendLayout();
            this.namePanel.SuspendLayout();
            this.phonePanel.SuspendLayout();
            this.emailPanel.SuspendLayout();
            this.addressPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // mainPanel
            // 
            this.mainPanel.Controls.Add(this.contentPanel);
            this.mainPanel.Controls.Add(this.headerPanel);
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Location = new Point(0, 0);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Size = new Size(500, 600);
            this.mainPanel.TabIndex = 0;
            
            // 
            // headerPanel
            // 
            this.headerPanel.Controls.Add(this.closeButton);
            this.headerPanel.Controls.Add(this.titleLabel);
            this.headerPanel.Dock = DockStyle.Top;
            this.headerPanel.Location = new Point(0, 0);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Size = new Size(500, 60);
            this.headerPanel.TabIndex = 0;
            
            // 
            // titleLabel
            // 
            this.titleLabel.Dock = DockStyle.Fill;
            this.titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            this.titleLabel.Location = new Point(0, 0);
            this.titleLabel.Name = "titleLabel";
            this.titleLabel.Size = new Size(440, 60);
            this.titleLabel.TabIndex = 0;
            this.titleLabel.Text = "إضافة عميل جديد";
            this.titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // 
            // closeButton
            // 
            this.closeButton.Dock = DockStyle.Right;
            this.closeButton.FlatStyle = FlatStyle.Flat;
            this.closeButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.closeButton.Location = new Point(440, 0);
            this.closeButton.Name = "closeButton";
            this.closeButton.Size = new Size(60, 60);
            this.closeButton.TabIndex = 1;
            this.closeButton.Text = "✕";
            this.closeButton.UseVisualStyleBackColor = true;
            this.closeButton.Click += new System.EventHandler(this.closeButton_Click);
            
            // 
            // contentPanel
            // 
            this.contentPanel.Controls.Add(this.formCard);
            this.contentPanel.Dock = DockStyle.Fill;
            this.contentPanel.Location = new Point(0, 60);
            this.contentPanel.Name = "contentPanel";
            this.contentPanel.Padding = new Padding(30);
            this.contentPanel.Size = new Size(500, 540);
            this.contentPanel.TabIndex = 1;
            
            // 
            // formCard
            // 
            this.formCard.Controls.Add(this.buttonPanel);
            this.formCard.Controls.Add(this.addressPanel);
            this.formCard.Controls.Add(this.emailPanel);
            this.formCard.Controls.Add(this.phonePanel);
            this.formCard.Controls.Add(this.namePanel);
            this.formCard.Dock = DockStyle.Fill;
            this.formCard.Location = new Point(30, 30);
            this.formCard.Name = "formCard";
            this.formCard.Padding = new Padding(30);
            this.formCard.Size = new Size(440, 480);
            this.formCard.TabIndex = 0;
            
            // 
            // namePanel
            // 
            this.namePanel.Controls.Add(this.nameTextBox);
            this.namePanel.Controls.Add(this.nameLabel);
            this.namePanel.Dock = DockStyle.Top;
            this.namePanel.Location = new Point(30, 30);
            this.namePanel.Name = "namePanel";
            this.namePanel.Size = new Size(380, 80);
            this.namePanel.TabIndex = 0;
            
            // 
            // nameLabel
            // 
            this.nameLabel.Dock = DockStyle.Top;
            this.nameLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.nameLabel.Location = new Point(0, 0);
            this.nameLabel.Name = "nameLabel";
            this.nameLabel.Size = new Size(380, 30);
            this.nameLabel.TabIndex = 0;
            this.nameLabel.Text = "اسم العميل *";
            this.nameLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // nameTextBox
            // 
            this.nameTextBox.Dock = DockStyle.Fill;
            this.nameTextBox.Font = new Font("Segoe UI", 12F);
            this.nameTextBox.Location = new Point(0, 30);
            this.nameTextBox.Name = "nameTextBox";
            this.nameTextBox.PlaceholderText = "أدخل اسم العميل";
            this.nameTextBox.Size = new Size(380, 29);
            this.nameTextBox.TabIndex = 1;
            
            // 
            // phonePanel
            // 
            this.phonePanel.Controls.Add(this.phoneTextBox);
            this.phonePanel.Controls.Add(this.phoneLabel);
            this.phonePanel.Dock = DockStyle.Top;
            this.phonePanel.Location = new Point(30, 110);
            this.phonePanel.Name = "phonePanel";
            this.phonePanel.Size = new Size(380, 80);
            this.phonePanel.TabIndex = 1;
            
            // 
            // phoneLabel
            // 
            this.phoneLabel.Dock = DockStyle.Top;
            this.phoneLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.phoneLabel.Location = new Point(0, 0);
            this.phoneLabel.Name = "phoneLabel";
            this.phoneLabel.Size = new Size(380, 30);
            this.phoneLabel.TabIndex = 0;
            this.phoneLabel.Text = "رقم الهاتف";
            this.phoneLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // phoneTextBox
            // 
            this.phoneTextBox.Dock = DockStyle.Fill;
            this.phoneTextBox.Font = new Font("Segoe UI", 12F);
            this.phoneTextBox.Location = new Point(0, 30);
            this.phoneTextBox.Name = "phoneTextBox";
            this.phoneTextBox.PlaceholderText = "أدخل رقم الهاتف";
            this.phoneTextBox.Size = new Size(380, 29);
            this.phoneTextBox.TabIndex = 1;
            
            // 
            // emailPanel
            // 
            this.emailPanel.Controls.Add(this.emailTextBox);
            this.emailPanel.Controls.Add(this.emailLabel);
            this.emailPanel.Dock = DockStyle.Top;
            this.emailPanel.Location = new Point(30, 190);
            this.emailPanel.Name = "emailPanel";
            this.emailPanel.Size = new Size(380, 80);
            this.emailPanel.TabIndex = 2;
            
            // 
            // emailLabel
            // 
            this.emailLabel.Dock = DockStyle.Top;
            this.emailLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.emailLabel.Location = new Point(0, 0);
            this.emailLabel.Name = "emailLabel";
            this.emailLabel.Size = new Size(380, 30);
            this.emailLabel.TabIndex = 0;
            this.emailLabel.Text = "البريد الإلكتروني";
            this.emailLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // emailTextBox
            // 
            this.emailTextBox.Dock = DockStyle.Fill;
            this.emailTextBox.Font = new Font("Segoe UI", 12F);
            this.emailTextBox.Location = new Point(0, 30);
            this.emailTextBox.Name = "emailTextBox";
            this.emailTextBox.PlaceholderText = "أدخل البريد الإلكتروني";
            this.emailTextBox.Size = new Size(380, 29);
            this.emailTextBox.TabIndex = 1;
            
            // 
            // addressPanel
            // 
            this.addressPanel.Controls.Add(this.addressTextBox);
            this.addressPanel.Controls.Add(this.addressLabel);
            this.addressPanel.Dock = DockStyle.Top;
            this.addressPanel.Location = new Point(30, 270);
            this.addressPanel.Name = "addressPanel";
            this.addressPanel.Size = new Size(380, 120);
            this.addressPanel.TabIndex = 3;
            
            // 
            // addressLabel
            // 
            this.addressLabel.Dock = DockStyle.Top;
            this.addressLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.addressLabel.Location = new Point(0, 0);
            this.addressLabel.Name = "addressLabel";
            this.addressLabel.Size = new Size(380, 30);
            this.addressLabel.TabIndex = 0;
            this.addressLabel.Text = "العنوان";
            this.addressLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // 
            // addressTextBox
            // 
            this.addressTextBox.Dock = DockStyle.Fill;
            this.addressTextBox.Font = new Font("Segoe UI", 12F);
            this.addressTextBox.Location = new Point(0, 30);
            this.addressTextBox.Multiline = true;
            this.addressTextBox.Name = "addressTextBox";
            this.addressTextBox.PlaceholderText = "أدخل العنوان";
            this.addressTextBox.ScrollBars = ScrollBars.Vertical;
            this.addressTextBox.Size = new Size(380, 90);
            this.addressTextBox.TabIndex = 1;
            
            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.clearButton);
            this.buttonPanel.Controls.Add(this.cancelButton);
            this.buttonPanel.Controls.Add(this.saveButton);
            this.buttonPanel.Dock = DockStyle.Bottom;
            this.buttonPanel.Location = new Point(30, 420);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(380, 60);
            this.buttonPanel.TabIndex = 4;
            
            // 
            // saveButton
            // 
            this.saveButton.Dock = DockStyle.Right;
            this.saveButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.saveButton.Location = new Point(260, 0);
            this.saveButton.Name = "saveButton";
            this.saveButton.Size = new Size(120, 60);
            this.saveButton.TabIndex = 0;
            this.saveButton.Text = "💾 حفظ";
            this.saveButton.UseVisualStyleBackColor = true;
            this.saveButton.Click += new System.EventHandler(this.saveButton_Click);
            
            // 
            // cancelButton
            // 
            this.cancelButton.Dock = DockStyle.Right;
            this.cancelButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.cancelButton.Location = new Point(140, 0);
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.Size = new Size(120, 60);
            this.cancelButton.TabIndex = 1;
            this.cancelButton.Text = "❌ إلغاء";
            this.cancelButton.UseVisualStyleBackColor = true;
            this.cancelButton.Click += new System.EventHandler(this.cancelButton_Click);
            
            // 
            // clearButton
            // 
            this.clearButton.Dock = DockStyle.Right;
            this.clearButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.clearButton.Location = new Point(20, 0);
            this.clearButton.Name = "clearButton";
            this.clearButton.Size = new Size(120, 60);
            this.clearButton.TabIndex = 2;
            this.clearButton.Text = "🗑️ مسح";
            this.clearButton.UseVisualStyleBackColor = true;
            this.clearButton.Click += new System.EventHandler(this.clearButton_Click);
            
            // 
            // AddCustomerForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 600);
            this.Controls.Add(this.mainPanel);
            this.FormBorderStyle = FormBorderStyle.None;
            this.Name = "AddCustomerForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة عميل جديد";
            this.Load += new System.EventHandler(this.AddCustomerForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            this.headerPanel.ResumeLayout(false);
            this.contentPanel.ResumeLayout(false);
            this.formCard.ResumeLayout(false);
            this.namePanel.ResumeLayout(false);
            this.namePanel.PerformLayout();
            this.phonePanel.ResumeLayout(false);
            this.phonePanel.PerformLayout();
            this.emailPanel.ResumeLayout(false);
            this.emailPanel.PerformLayout();
            this.addressPanel.ResumeLayout(false);
            this.addressPanel.PerformLayout();
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private Panel mainPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Button closeButton;
        private Panel contentPanel;
        private Panel formCard;
        private Panel namePanel;
        private Label nameLabel;
        private TextBox nameTextBox;
        private Panel phonePanel;
        private Label phoneLabel;
        private TextBox phoneTextBox;
        private Panel emailPanel;
        private Label emailLabel;
        private TextBox emailTextBox;
        private Panel addressPanel;
        private Label addressLabel;
        private TextBox addressTextBox;
        private Panel buttonPanel;
        private Button saveButton;
        private Button cancelButton;
        private Button clearButton;
    }
}
