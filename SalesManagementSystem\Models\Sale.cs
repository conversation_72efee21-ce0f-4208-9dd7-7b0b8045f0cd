using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Sale
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public Customer Customer { get; set; }
        public DateTime SaleDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Discount { get; set; }
        public decimal FinalAmount { get; set; }
        public string Notes { get; set; }
        public List<SaleItem> SaleItems { get; set; }
        
        public Sale()
        {
            SaleDate = DateTime.Now;
            SaleItems = new List<SaleItem>();
        }
    }
    
    public class SaleItem
    {
        public int Id { get; set; }
        public int SaleId { get; set; }
        public int ProductId { get; set; }
        public Product Product { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        
        public SaleItem()
        {
            Quantity = 1;
        }
    }
}
