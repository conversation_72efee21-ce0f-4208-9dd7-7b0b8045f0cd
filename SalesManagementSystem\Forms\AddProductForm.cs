using System;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using SalesManagementSystem.DAL;
using SalesManagementSystem.Models;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class AddProductForm : Form
    {
        private ProductDAL productDAL;
        private Product? productToEdit;
        private bool isEditMode = false;
        
        public AddProductForm()
        {
            InitializeComponent();
            productDAL = new ProductDAL();
        }
        
        public AddProductForm(Product product) : this()
        {
            productToEdit = product;
            isEditMode = true;
        }
        
        private void AddProductForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            ApplyDarkTheme();
            
            // إعداد النموذج
            SetupForm();
            
            // تحميل الفئات
            LoadCategories();
            
            // إذا كان في وضع التعديل، تحميل بيانات المنتج
            if (isEditMode && productToEdit != null)
            {
                LoadProductData();
            }
            
            // التركيز على حقل الاسم
            nameTextBox.Focus();
        }
        
        private void ApplyDarkTheme()
        {
            // تطبيق النمط الداكن على النموذج
            this.BackColor = DarkTheme.BackgroundColor;
            this.ForeColor = DarkTheme.TextColor;
            
            // الشريط العلوي
            headerPanel.BackColor = DarkTheme.PrimaryColor;
            titleLabel.ForeColor = Color.White;
            
            // زر الإغلاق
            closeButton.BackColor = DarkTheme.ErrorColor;
            closeButton.ForeColor = Color.White;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(200, 50, 50);
            
            // البطاقة الرئيسية
            formCard.BackColor = DarkTheme.CardColor;
            
            // حقول الإدخال
            ApplyTextBoxTheme(nameTextBox);
            ApplyTextBoxTheme(descriptionTextBox);
            ApplyTextBoxTheme(priceTextBox);
            ApplyTextBoxTheme(stockTextBox);
            ApplyComboBoxTheme(categoryComboBox);
            
            // التسميات
            nameLabel.ForeColor = DarkTheme.TextColor;
            descriptionLabel.ForeColor = DarkTheme.TextColor;
            priceLabel.ForeColor = DarkTheme.TextColor;
            stockLabel.ForeColor = DarkTheme.TextColor;
            categoryLabel.ForeColor = DarkTheme.TextColor;
            
            // الأزرار
            SetupButtons();
        }
        
        private void ApplyTextBoxTheme(TextBox textBox)
        {
            textBox.BackColor = DarkTheme.SurfaceColor;
            textBox.ForeColor = DarkTheme.TextColor;
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }
        
        private void ApplyComboBoxTheme(ComboBox comboBox)
        {
            comboBox.BackColor = DarkTheme.SurfaceColor;
            comboBox.ForeColor = DarkTheme.TextColor;
            comboBox.FlatStyle = FlatStyle.Flat;
        }
        
        private void SetupButtons()
        {
            // زر الحفظ
            saveButton.BackColor = DarkTheme.SuccessColor;
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(40, 180, 70);
            
            // زر الإلغاء
            cancelButton.BackColor = DarkTheme.BorderColor;
            cancelButton.ForeColor = Color.White;
            cancelButton.FlatStyle = FlatStyle.Flat;
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(100, 100, 100);
            
            // زر المسح
            clearButton.BackColor = DarkTheme.AccentColor;
            clearButton.ForeColor = Color.White;
            clearButton.FlatStyle = FlatStyle.Flat;
            clearButton.FlatAppearance.BorderSize = 0;
            clearButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 140, 0);
        }
        
        private void SetupForm()
        {
            // تحديث العنوان حسب الوضع
            if (isEditMode)
            {
                titleLabel.Text = "تعديل بيانات المنتج";
                saveButton.Text = "💾 تحديث";
            }
            else
            {
                titleLabel.Text = "إضافة منتج جديد";
                saveButton.Text = "💾 حفظ";
            }
            
            // إضافة تأثير الظل للنموذج
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.Magenta;
            this.TransparencyKey = Color.Magenta;
            
            // إضافة حدود للبطاقة
            formCard.BorderStyle = BorderStyle.FixedSingle;
        }
        
        private void LoadCategories()
        {
            try
            {
                var categories = productDAL.GetCategories();
                
                // إضافة فئات افتراضية إذا لم توجد
                if (categories.Count == 0)
                {
                    categories.AddRange(new[] { "إلكترونيات", "إكسسوارات", "ملابس", "أدوات منزلية", "كتب", "أخرى" });
                }
                
                categoryComboBox.Items.Clear();
                foreach (var category in categories)
                {
                    categoryComboBox.Items.Add(category);
                }
                
                // إضافة خيار "أخرى" إذا لم يكن موجوداً
                if (!categoryComboBox.Items.Contains("أخرى"))
                {
                    categoryComboBox.Items.Add("أخرى");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void LoadProductData()
        {
            if (productToEdit != null)
            {
                nameTextBox.Text = productToEdit.Name;
                descriptionTextBox.Text = productToEdit.Description;
                priceTextBox.Text = productToEdit.Price.ToString("F2");
                stockTextBox.Text = productToEdit.Stock.ToString();
                
                // تحديد الفئة
                if (!string.IsNullOrEmpty(productToEdit.Category))
                {
                    categoryComboBox.Text = productToEdit.Category;
                }
            }
        }
        
        private void ClearForm()
        {
            nameTextBox.Clear();
            descriptionTextBox.Clear();
            priceTextBox.Clear();
            stockTextBox.Clear();
            categoryComboBox.SelectedIndex = -1;
            categoryComboBox.Text = "";
            nameTextBox.Focus();
        }
        
        private bool ValidateForm()
        {
            // التحقق من الاسم (مطلوب)
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }
            
            // التحقق من السعر (مطلوب)
            if (!decimal.TryParse(priceTextBox.Text, out decimal price) || price < 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                priceTextBox.Focus();
                return false;
            }
            
            // التحقق من المخزون (مطلوب)
            if (!int.TryParse(stockTextBox.Text, out int stock) || stock < 0)
            {
                MessageBox.Show("يرجى إدخال كمية مخزون صحيحة", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                stockTextBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private Product GetProductFromForm()
        {
            return new Product
            {
                Id = isEditMode && productToEdit != null ? productToEdit.Id : 0,
                Name = nameTextBox.Text.Trim(),
                Description = descriptionTextBox.Text.Trim(),
                Price = decimal.Parse(priceTextBox.Text),
                Stock = int.Parse(stockTextBox.Text),
                Category = categoryComboBox.Text.Trim()
            };
        }
        
        // أحداث الأزرار
        private void saveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;
            
            try
            {
                var product = GetProductFromForm();
                
                if (isEditMode)
                {
                    // تحديث المنتج
                    if (productDAL.UpdateProduct(product))
                    {
                        MessageBox.Show("تم تحديث بيانات المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    // إضافة منتج جديد
                    int newId = productDAL.AddProduct(product);
                    if (newId > 0)
                    {
                        MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        // سؤال المستخدم إذا كان يريد إضافة منتج آخر
                        var result = MessageBox.Show("هل تريد إضافة منتج آخر؟", "إضافة منتج آخر", 
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        
                        if (result == DialogResult.Yes)
                        {
                            ClearForm();
                        }
                        else
                        {
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void cancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        private void clearButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من مسح جميع البيانات؟", "تأكيد المسح", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                ClearForm();
            }
        }
        
        private void closeButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        // التحقق من إدخال الأرقام فقط في حقل السعر
        private void priceTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح بالأرقام والنقطة العشرية وزر Backspace
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }
            
            // السماح بنقطة عشرية واحدة فقط
            if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }
        }
        
        // التحقق من إدخال الأرقام فقط في حقل المخزون
        private void stockTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح بالأرقام وزر Backspace فقط
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }
        
        // إضافة تأثيرات بصرية
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            // رسم ظل للنموذج
            var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0));
            e.Graphics.FillRectangle(shadowBrush, 5, 5, this.Width - 5, this.Height - 5);
            shadowBrush.Dispose();
        }
    }
}
