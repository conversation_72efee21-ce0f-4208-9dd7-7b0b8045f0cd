using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Reporting.WinForms;
using SalesManagementSystem.DAL;
using SalesManagementSystem.UI;

namespace SalesManagementSystem.Forms
{
    public partial class ReportsForm : Form
    {
        private SaleDAL saleDAL;
        private CustomerDAL customerDAL;
        
        public ReportsForm()
        {
            InitializeComponent();
            saleDAL = new SaleDAL();
            customerDAL = new CustomerDAL();
        }
        
        private void ReportsForm_Load(object sender, EventArgs e)
        {
            // تطبيق النمط الداكن
            DarkTheme.ApplyToForm(this);
            
            // تخصيص الأزرار
            SetupButtons();
            
            // تعيين التواريخ الافتراضية
            endDatePicker.Value = DateTime.Today;
            startDatePicker.Value = DateTime.Today.AddDays(-30);
            
            // إعداد عارض التقارير
            SetupReportViewer();
        }
        
        private void SetupButtons()
        {
            DarkTheme.ApplyToButton(generateButton);
            generateButton.BackColor = DarkTheme.PrimaryColor;
            
            DarkTheme.ApplyToButton(exportButton);
            exportButton.BackColor = DarkTheme.AccentColor;
            
            DarkTheme.ApplyToButton(printButton);
            printButton.BackColor = DarkTheme.SuccessColor;
        }
        
        private void SetupReportViewer()
        {
            try
            {
                reportViewer.ProcessingMode = ProcessingMode.Local;
                reportViewer.LocalReport.ReportPath = Path.Combine(Application.StartupPath, "Reports", "SalesReport.rdlc");
                
                // إخفاء شريط الأدوات الافتراضي
                reportViewer.ShowToolBar = true;
                reportViewer.ShowParameterPrompts = false;
                
                // تطبيق النمط الداكن على عارض التقارير
                reportViewer.BackColor = DarkTheme.BackgroundColor;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد عارض التقارير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void generateButton_Click(object sender, EventArgs e)
        {
            try
            {
                GenerateReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void GenerateReport()
        {
            // الحصول على البيانات
            var sales = saleDAL.GetSalesByDateRange(startDatePicker.Value, endDatePicker.Value);
            
            // إنشاء DataTable للتقرير
            DataTable salesTable = new DataTable("SalesDataSet");
            salesTable.Columns.Add("Id", typeof(int));
            salesTable.Columns.Add("CustomerName", typeof(string));
            salesTable.Columns.Add("SaleDate", typeof(DateTime));
            salesTable.Columns.Add("TotalAmount", typeof(decimal));
            salesTable.Columns.Add("Discount", typeof(decimal));
            salesTable.Columns.Add("FinalAmount", typeof(decimal));
            
            // ملء البيانات
            foreach (var sale in sales)
            {
                salesTable.Rows.Add(
                    sale.Id,
                    sale.Customer?.Name ?? "غير محدد",
                    sale.SaleDate,
                    sale.TotalAmount,
                    sale.Discount,
                    sale.FinalAmount
                );
            }
            
            // إعداد التقرير
            reportViewer.LocalReport.DataSources.Clear();
            ReportDataSource dataSource = new ReportDataSource("SalesDataSet", salesTable);
            reportViewer.LocalReport.DataSources.Add(dataSource);
            
            // إعداد المعاملات
            ReportParameter[] parameters = new ReportParameter[]
            {
                new ReportParameter("ReportTitle", $"تقرير المبيعات من {startDatePicker.Value:dd/MM/yyyy} إلى {endDatePicker.Value:dd/MM/yyyy}")
            };
            reportViewer.LocalReport.SetParameters(parameters);
            
            // تحديث التقرير
            reportViewer.RefreshReport();
            
            // تفعيل أزرار التصدير والطباعة
            exportButton.Enabled = true;
            printButton.Enabled = true;
            
            // عرض إحصائيات سريعة
            decimal totalSales = sales.Sum(s => s.FinalAmount);
            int salesCount = sales.Count;
            
            MessageBox.Show($"تم إنشاء التقرير بنجاح\n\nعدد المبيعات: {salesCount}\nإجمالي المبيعات: {totalSales:C}", 
                "تم إنشاء التقرير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void exportButton_Click(object sender, EventArgs e)
        {
            try
            {
                ExportToPDF();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ExportToPDF()
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = "PDF Files|*.pdf";
            saveDialog.Title = "حفظ التقرير كـ PDF";
            saveDialog.FileName = $"تقرير_المبيعات_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            
            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    byte[] bytes = reportViewer.LocalReport.Render("PDF");
                    File.WriteAllBytes(saveDialog.FileName, bytes);
                    
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // فتح الملف
                    var result = MessageBox.Show("هل تريد فتح الملف؟", "فتح الملف", 
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الملف: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private void printButton_Click(object sender, EventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintReport()
        {
            // إعداد الطباعة
            reportViewer.PrintDialog();
        }
    }
}
