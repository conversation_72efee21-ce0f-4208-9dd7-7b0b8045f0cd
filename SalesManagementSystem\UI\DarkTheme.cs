using System;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.UI
{
    public static class DarkTheme
    {
        // الألوان الأساسية للنمط الداكن
        public static readonly Color BackgroundColor = Color.FromArgb(32, 32, 32);
        public static readonly Color SurfaceColor = Color.FromArgb(48, 48, 48);
        public static readonly Color CardColor = Color.FromArgb(64, 64, 64);
        public static readonly Color PrimaryColor = Color.FromArgb(0, 122, 255);
        public static readonly Color SecondaryColor = Color.FromArgb(88, 166, 255);
        public static readonly Color AccentColor = Color.FromArgb(255, 159, 10);
        public static readonly Color TextColor = Color.FromArgb(255, 255, 255);
        public static readonly Color SecondaryTextColor = Color.FromArgb(180, 180, 180);
        public static readonly Color BorderColor = Color.FromArgb(80, 80, 80);
        public static readonly Color HoverColor = Color.FromArgb(72, 72, 72);
        public static readonly Color SuccessColor = Color.FromArgb(52, 199, 89);
        public static readonly Color WarningColor = Color.FromArgb(255, 204, 0);
        public static readonly Color ErrorColor = Color.FromArgb(255, 69, 58);
        
        // الخطوط
        public static readonly Font HeaderFont = new Font("Segoe UI", 16F, FontStyle.Bold);
        public static readonly Font SubHeaderFont = new Font("Segoe UI", 12F, FontStyle.Bold);
        public static readonly Font RegularFont = new Font("Segoe UI", 10F, FontStyle.Regular);
        public static readonly Font SmallFont = new Font("Segoe UI", 9F, FontStyle.Regular);
        
        public static void ApplyToForm(Form form)
        {
            form.BackColor = BackgroundColor;
            form.ForeColor = TextColor;
            form.Font = RegularFont;
            
            ApplyToControls(form.Controls);
        }
        
        public static void ApplyToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                ApplyToControl(control);
                
                if (control.HasChildren)
                {
                    ApplyToControls(control.Controls);
                }
            }
        }
        
        public static void ApplyToControl(Control control)
        {
            switch (control)
            {
                case Form form:
                    ApplyToForm(form);
                    break;
                    
                case Panel panel:
                    panel.BackColor = SurfaceColor;
                    panel.ForeColor = TextColor;
                    break;
                    
                case GroupBox groupBox:
                    groupBox.BackColor = SurfaceColor;
                    groupBox.ForeColor = TextColor;
                    break;
                    
                case Button button:
                    ApplyToButton(button);
                    break;
                    
                case TextBox textBox:
                    ApplyToTextBox(textBox);
                    break;
                    
                case ComboBox comboBox:
                    ApplyToComboBox(comboBox);
                    break;
                    
                case DataGridView dataGridView:
                    ApplyToDataGridView(dataGridView);
                    break;
                    
                case Label label:
                    label.BackColor = Color.Transparent;
                    label.ForeColor = TextColor;
                    break;
                    
                case MenuStrip menuStrip:
                    ApplyToMenuStrip(menuStrip);
                    break;
                    
                case StatusStrip statusStrip:
                    ApplyToStatusStrip(statusStrip);
                    break;
                    
                case TabControl tabControl:
                    ApplyToTabControl(tabControl);
                    break;
                    
                default:
                    control.BackColor = SurfaceColor;
                    control.ForeColor = TextColor;
                    break;
            }
        }
        
        public static void ApplyToButton(Button button)
        {
            button.BackColor = PrimaryColor;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = SecondaryColor;
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(0, 100, 200);
            button.Font = RegularFont;
            button.Cursor = Cursors.Hand;
        }
        
        public static void ApplyToTextBox(TextBox textBox)
        {
            textBox.BackColor = CardColor;
            textBox.ForeColor = TextColor;
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.Font = RegularFont;
        }
        
        public static void ApplyToComboBox(ComboBox comboBox)
        {
            comboBox.BackColor = CardColor;
            comboBox.ForeColor = TextColor;
            comboBox.FlatStyle = FlatStyle.Flat;
            comboBox.Font = RegularFont;
        }
        
        public static void ApplyToDataGridView(DataGridView dataGridView)
        {
            dataGridView.BackgroundColor = SurfaceColor;
            dataGridView.GridColor = BorderColor;
            dataGridView.DefaultCellStyle.BackColor = CardColor;
            dataGridView.DefaultCellStyle.ForeColor = TextColor;
            dataGridView.DefaultCellStyle.SelectionBackColor = PrimaryColor;
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = SurfaceColor;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = TextColor;
            dataGridView.RowHeadersDefaultCellStyle.BackColor = SurfaceColor;
            dataGridView.RowHeadersDefaultCellStyle.ForeColor = TextColor;
            dataGridView.EnableHeadersVisualStyles = false;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dataGridView.Font = RegularFont;
        }
        
        public static void ApplyToMenuStrip(MenuStrip menuStrip)
        {
            menuStrip.BackColor = SurfaceColor;
            menuStrip.ForeColor = TextColor;
            menuStrip.Font = RegularFont;
            
            foreach (ToolStripMenuItem item in menuStrip.Items)
            {
                ApplyToMenuItem(item);
            }
        }
        
        public static void ApplyToMenuItem(ToolStripMenuItem menuItem)
        {
            menuItem.BackColor = SurfaceColor;
            menuItem.ForeColor = TextColor;
            
            if (menuItem.DropDown != null)
            {
                menuItem.DropDown.BackColor = SurfaceColor;
                menuItem.DropDown.ForeColor = TextColor;
                
                foreach (ToolStripItem subItem in menuItem.DropDownItems)
                {
                    if (subItem is ToolStripMenuItem subMenuItem)
                    {
                        ApplyToMenuItem(subMenuItem);
                    }
                }
            }
        }
        
        public static void ApplyToStatusStrip(StatusStrip statusStrip)
        {
            statusStrip.BackColor = SurfaceColor;
            statusStrip.ForeColor = TextColor;
            statusStrip.Font = RegularFont;
        }
        
        public static void ApplyToTabControl(TabControl tabControl)
        {
            tabControl.BackColor = SurfaceColor;
            tabControl.ForeColor = TextColor;
            tabControl.Font = RegularFont;
            
            foreach (TabPage tabPage in tabControl.TabPages)
            {
                tabPage.BackColor = BackgroundColor;
                tabPage.ForeColor = TextColor;
            }
        }
        
        public static Button CreateStyledButton(string text, Color? backgroundColor = null)
        {
            var button = new Button
            {
                Text = text,
                BackColor = backgroundColor ?? PrimaryColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = RegularFont,
                Cursor = Cursors.Hand,
                Size = new Size(120, 35)
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = SecondaryColor;
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(0, 100, 200);
            
            return button;
        }
        
        public static Panel CreateCard()
        {
            return new Panel
            {
                BackColor = CardColor,
                Padding = new Padding(15),
                Margin = new Padding(10)
            };
        }
    }
}
